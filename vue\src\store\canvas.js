import { canvasManager } from '@/utils/canvas';
import canvasSocketManager from '@/utils/canvasSocket';
import { defineStore } from 'pinia';

export const useCanvasStore = defineStore('canvas', {
  state: () => ({
    showCanvas: false, // 🔥 确保初始状态为隐藏
    eraseSize: 30,
    showEraseIndicator: true,
    mouseX: 0,
    mouseY: 0,
    penPressure: 0,
    penTiltX: 0,
    penTiltY: 0,
    penSupported: false,
    isEraseMode: false,
    currentId: null, // 用于存储当前题目的ID，以便画布数据保存/加载

    // 🚀 Socket.IO实时传输相关状态
    socketConnected: false,
    currentRoomId: null,
    activeUsers: 0,
    realtimeEnabled: false, // 是否启用实时传输
    lastSyncTime: null,
  }),
  actions: {
    // 设置当前题目的ID
    setCurrentQuestionId(id) {
      this.currentId = id;
      canvasManager.setCurrentId(id); // 同步到 canvasManager
    },

    // 初始化画布
    async initCanvas() {
      // 初始化桌面端画布
      canvasManager.initCanvas('drawingCanvas', 'indicatorCanvas', '.wp');

      // 同步 canvasManager 的状态到 Pinia Store
      const state = canvasManager.getCanvasState();
      this.penSupported = state.penSupported;
      this.isEraseMode = state.isEraseMode;
      this.eraseSize = state.eraseSize;
      this.showEraseIndicator = state.showEraseIndicator;
      this.mouseX = state.mouseX;
      this.mouseY = state.mouseY;
      this.penPressure = state.penPressure;
      this.penTiltX = state.penTiltX;
      this.penTiltY = state.penTiltY;

      // 🚀 初始化Socket.IO连接（如果启用实时传输）
      if (this.realtimeEnabled) {
        await this.initRealtimeConnection();
      }
    },

    async clearCanvas() {
      await canvasManager.clearCanvas();
    },

    async saveCanvasData(immediate = false) {
      // 保存前确保 currentId 已设置
      if (!this.currentId) {
        console.warn('CanvasStore: currentId is not set. Cannot save canvas data.');
        return;
      }
      await canvasManager.saveCanvasData(immediate);
    },

    async loadCanvasData() {
      // 加载前确保 currentId 已设置
      if (!this.currentId) {
        console.warn('CanvasStore: currentId is not set. Cannot load canvas data.');
        return;
      }
      await canvasManager.loadCanvasData();
    },

    startDrawing(event) {
      canvasManager.startDrawing(event);
    },

    async stopDrawing() {
      await canvasManager.stopDrawing();
    },

    draw(event) {
      canvasManager.draw(event);
    },

    updateMousePosition(event) {
      canvasManager.updateMousePosition(event);
      // 同步状态
      const state = canvasManager.getCanvasState();
      this.mouseX = state.mouseX;
      this.mouseY = state.mouseY;
    },

    handleMouseMove(event) {
      canvasManager.handleMouseMove(event);
      // 同步状态
      const state = canvasManager.getCanvasState();
      this.mouseX = state.mouseX;
      this.mouseY = state.mouseY;
      this.penPressure = state.penPressure;
      this.penTiltX = state.penTiltX;
      this.penTiltY = state.penTiltY;
    },

    handleMouseLeave() {
      canvasManager.handleMouseLeave();
    },

    async startDrawingTouch(event) {
      return await canvasManager.startDrawingTouch(event);
    },

    async drawTouch(event) {
      return await canvasManager.drawTouch(event);
    },

    async handlePointerDown(event) {
      const result = await canvasManager.handlePointerDown(event);
      const state = canvasManager.getCanvasState();
      this.isEraseMode = state.isEraseMode;
      this.penPressure = state.penPressure;
      this.penTiltX = state.penTiltX;
      this.penTiltY = state.penTiltY;
      return result;
    },

    async handlePointerMove(event) {
      const result = await canvasManager.handlePointerMove(event);
      const state = canvasManager.getCanvasState();
      this.mouseX = state.mouseX;
      this.mouseY = state.mouseY;
      this.penPressure = state.penPressure;
      this.penTiltX = state.penTiltX;
      this.penTiltY = state.penTiltY;
      return result;
    },

    async handlePointerUp(event) {
      await canvasManager.handlePointerUp(event);
      const state = canvasManager.getCanvasState();
      this.isEraseMode = state.isEraseMode;
    },

    async handlePointerLeave(event) {
      await canvasManager.handlePointerLeave(event);
    },

    async toggleDrawMode() {
      console.log('CanvasStore: toggleDrawMode called, current isEraseMode:', this.isEraseMode);
      await canvasManager.toggleDrawMode();
      const state = canvasManager.getCanvasState();
      this.isEraseMode = state.isEraseMode;
      console.log('CanvasStore: toggleDrawMode completed, new isEraseMode:', this.isEraseMode);
    },

    // 控制画布的显示/隐藏，并负责加载/保存数据 - 增强版本
    async toggleCanvasVisibility() {
      console.log(`🎨 画布可见性切换: ${this.showCanvas} -> ${!this.showCanvas}`);

      // 如果当前画布可见，先保存数据
      if (this.showCanvas && this.currentId) {
        console.log('💾 保存当前画布数据...');
        await this.saveCanvasData(true);
      }

      this.showCanvas = !this.showCanvas;

      if (this.showCanvas) {
        if (!this.currentId) {
          console.warn('⚠️ 没有设置currentId，无法初始化画布');
          return;
        }

        console.log('🔄 初始化画布...');
        await this.initCanvas(); // 关键：每次显示都先初始化画布

        // 🔥 修复：只有在用户主动打开画布时才加载数据
        if (this.showCanvas) {
          console.log('📥 加载画布数据...');
          await this.loadCanvasData();
        } else {
          console.log('💡 画布已隐藏，跳过数据加载');
        }
      } else {
        // 🔥 画布隐藏时重置初始化状态，防止重复画布
        console.log('🔄 重置画布初始化状态');
        if (canvasManager) {
          canvasManager.isInitialized = false;
        }
      }
    },

    // 直接更新擦除范围
    updateEraseSize(size) {
      this.eraseSize = size;
      canvasManager.eraseSize = size;
    },

    // 切换擦除指示器显示
    toggleEraseIndicator() {
      this.showEraseIndicator = !this.showEraseIndicator;
      canvasManager.showEraseIndicator = this.showEraseIndicator;
    },

    // 在组件卸载前清理 canvasManager 资源
    cleanupCanvas() {
      canvasManager.cleanup();
      // 🔌 断开Socket.IO连接
      if (this.socketConnected) {
        canvasSocketManager.disconnect();
        this.socketConnected = false;
      }
    },

    // 🚀 ===== Socket.IO实时传输相关方法 =====

    /**
     * 🚀 启用实时传输功能 - 单用户增量保存模式
     */
    async enableRealtimeTransmission() {
      console.log('🚀 启用画布实时传输功能 (单用户增量保存模式)');

      // 🔥 修复：单用户也需要增量保存，启用轻量级实时传输
      console.log('💡 单用户模式：启用增量保存，禁用多用户房间功能');

      this.realtimeEnabled = true; // 启用增量保存
      this.singleUserMode = true; // 标记为单用户模式

      // 初始化连接但跳过房间功能
      if (this.currentId) {
        await this.initRealtimeConnection();
      }
    },

    /**
     * 禁用实时传输功能
     */
    async disableRealtimeTransmission() {
      console.log('🔌 禁用画布实时传输功能');
      this.realtimeEnabled = false;

      if (this.socketConnected) {
        await canvasSocketManager.leaveRoom();
        canvasSocketManager.disconnect();
        this.socketConnected = false;
        this.currentRoomId = null;
        this.activeUsers = 0;
      }
    },

    /**
     * 初始化实时连接
     */
    async initRealtimeConnection() {
      if (!this.realtimeEnabled || !this.currentId) {
        return;
      }

      try {
        console.log('🔗 初始化Socket.IO连接...');

        // 连接到Socket.IO服务器
        await canvasSocketManager.connect();

        // 设置事件监听器
        this.setupSocketEventListeners();

        // 🚀 设置画布传输回调
        if (this.singleUserMode) {
          this.setupSingleUserCallback();
        } else {
          this.setupCanvasRealtimeCallback();
        }

        // 🔥 单用户模式：跳过房间功能，直接启用增量保存
        if (this.singleUserMode) {
          console.log('💡 单用户模式：跳过房间加入，启用增量保存');
          this.socketConnected = true; // 标记为已连接状态
        } else {
          // 多用户模式：加入房间
          await canvasSocketManager.joinRoom(this.currentId, {
            name: '用户' + Math.random().toString(36).substr(2, 4),
            device: canvasSocketManager.getDeviceInfo(),
          });
        }
      } catch (error) {
        console.error('❌ 初始化实时连接失败:', error);
      }
    },

    /**
     * 🚀 设置单用户增量保存回调
     */
    setupSingleUserCallback() {
      // 设置全局回调函数，供canvas.js调用
      if (typeof window !== 'undefined') {
        window.canvasRealtimeCallback = (newStrokes) => {
          console.log('💾 单用户增量保存:', newStrokes.length, '个笔画');
          // 单用户模式：执行增量保存 + 数据库保存
          this.handleSingleUserUpdate(newStrokes);
        };
      }
    },

    /**
     * 🚀 设置画布实时传输回调 (已禁用)
     */
    setupCanvasRealtimeCallback() {
      console.log('⚠️ 实时传输回调已禁用 (单用户模式)');
      // 在单用户模式下不设置实时传输回调
    },

    /**
     * 设置Socket.IO事件监听器
     */
    setupSocketEventListeners() {
      // 连接成功
      canvasSocketManager.on('connected', (data) => {
        console.log('✅ Socket.IO连接成功:', data);
        this.socketConnected = true;
      });

      // 连接断开
      canvasSocketManager.on('disconnected', (data) => {
        console.log('❌ Socket.IO连接断开:', data);
        this.socketConnected = false;
        this.activeUsers = 0;
      });

      // 成功加入房间
      canvasSocketManager.on('room_joined', (data) => {
        console.log('🏠 成功加入房间:', data);
        this.currentRoomId = data.roomId;
        this.activeUsers = data.activeUsers || 1;
      });

      // 用户加入房间
      canvasSocketManager.on('user_joined', (data) => {
        console.log('👤 用户加入房间:', data);
        this.activeUsers = data.activeUsers || this.activeUsers + 1;
      });

      // 用户离开房间
      canvasSocketManager.on('user_left', (data) => {
        console.log('👋 用户离开房间:', data);
        this.activeUsers = Math.max(0, data.activeUsers || this.activeUsers - 1);
      });

      // 接收画布更新
      canvasSocketManager.on('canvas_update', (data) => {
        console.log('🎨 接收远程画布更新:', data);
        this.handleRemoteCanvasUpdate(data);
      });

      // 接收完整同步
      canvasSocketManager.on('full_sync', (data) => {
        console.log('🔄 接收完整同步:', data);
        this.handleFullSync(data);
      });

      // 错误处理
      canvasSocketManager.on('error', (error) => {
        console.error('❌ Socket.IO错误:', error);
      });
    },

    /**
     * 处理远程画布更新
     */
    handleRemoteCanvasUpdate(data) {
      if (!data.strokes || !Array.isArray(data.strokes)) {
        return;
      }

      try {
        // 将远程笔画添加到本地画布
        data.strokes.forEach((stroke) => {
          canvasManager.addRemoteStroke(stroke);
        });

        this.lastSyncTime = Date.now();
      } catch (error) {
        console.error('处理远程画布更新失败:', error);
      }
    },

    /**
     * 处理完整同步 (已禁用 - 单用户模式)
     */
    handleFullSync(data) {
      console.log('⚠️ 单用户模式：跳过远程完整同步，避免数据冲突');

      // 🔥 单用户模式：禁用远程数据同步，避免覆盖本地数据
      // if (!data.canvasData) {
      //   return;
      // }

      // try {
      //   // 加载完整的画布数据
      //   canvasManager.loadCanvasDataFromRemote(data.canvasData);
      //   this.lastSyncTime = Date.now();
      // } catch (error) {
      //   console.error('处理完整同步失败:', error);
      // }
    },

    /**
     * 🚀 处理单用户更新 - 增量保存模式
     */
    async handleSingleUserUpdate(newStrokes) {
      try {
        console.log('🚀 处理单用户增量更新:', newStrokes.length, '个笔画');

        // 1. 立即触发数据库保存
        if (canvasManager && canvasManager.performSave) {
          await canvasManager.performSave();
          console.log('✅ 增量数据已保存到数据库');
        }

        // 2. 可选：触发本地存储备份
        this.saveToLocalStorage(newStrokes);
      } catch (error) {
        console.error('❌ 单用户更新处理失败:', error);
      }
    },

    /**
     * 💾 保存到本地存储 (备份机制)
     */
    saveToLocalStorage(strokes) {
      try {
        const backupData = {
          timestamp: Date.now(),
          strokes: strokes,
          canvasId: this.currentId,
        };
        localStorage.setItem(`canvas_backup_${this.currentId}`, JSON.stringify(backupData));
        console.log('💾 已备份到本地存储');
      } catch (error) {
        console.warn('⚠️ 本地存储备份失败:', error);
      }
    },

    /**
     * 发送画布更新 - 支持单用户和多用户模式
     */
    async sendCanvasUpdate(strokes) {
      if (this.singleUserMode) {
        // 单用户模式：执行增量保存
        console.log('💾 单用户模式：执行增量保存');
        await this.handleSingleUserUpdate(strokes);
        return;
      }

      // 多用户模式：发送到Socket.IO
      if (!this.realtimeEnabled || !this.socketConnected || !strokes.length) {
        return;
      }

      try {
        await canvasSocketManager.sendCanvasUpdate(strokes, {
          timestamp: Date.now(),
          device: canvasSocketManager.getDeviceInfo().platform,
        });
      } catch (error) {
        console.error('发送画布更新失败:', error);
      }
    },

    /**
     * 获取实时传输状态
     */
    getRealtimeStatus() {
      return {
        enabled: this.realtimeEnabled,
        connected: this.socketConnected,
        roomId: this.currentRoomId,
        activeUsers: this.activeUsers,
        lastSync: this.lastSyncTime,
        socketStatus: canvasSocketManager.getStatus(),
      };
    },
  },
});
