name: 🚀 Vue3 快速部署

on:
  push:
    branches: [main]
    paths-ignore:
      - "**.md"
      - "**.ps1"
      - "**/README.md"
  workflow_dispatch:

jobs:
  deploy:
    name: 快速构建部署
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 记录开始时间
        id: start-time
        run: echo "start=$(date +%s)" >> $GITHUB_OUTPUT

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: 设置 pnpm
        uses: pnpm/action-setup@v4
        with:
          version: "9"
          run_install: false

      - name: 获取 pnpm store 目录
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: 智能缓存策略
        uses: actions/cache@v4
        with:
          path: |
            ${{ env.STORE_PATH }}
            node_modules
            .vite
            dist
          key: ${{ runner.os }}-vue-${{ hashFiles('pnpm-lock.yaml', 'vite.config.js', 'src/**/*') }}
          restore-keys: |
            ${{ runner.os }}-vue-${{ hashFiles('pnpm-lock.yaml', 'vite.config.js') }}
            ${{ runner.os }}-vue-${{ hashFiles('pnpm-lock.yaml') }}
            ${{ runner.os }}-vue-

      - name: 安装依赖
        run: |
          echo "📦 安装依赖..."
          pnpm install --frozen-lockfile --prefer-offline --reporter=silent
          echo "✅ 依赖安装完成"

      - name: 超快速构建
        env:
          NODE_ENV: production
          VITE_BUILD_ANALYZE: false
          NODE_OPTIONS: "--max-old-space-size=6144"
          CI: true
        run: |
          echo "🚀 开始超快速构建..."
          time pnpm build:fast
          echo "✅ 构建完成"

      - name: 获取构建信息
        id: build-info
        run: |
          BUILD_SIZE=$(du -sh dist/ | cut -f1)
          echo "size=$BUILD_SIZE" >> $GITHUB_OUTPUT

          # 获取最近的提交信息并转义特殊字符
          COMMIT_MSG=$(git log -1 --pretty=format:"%s" | sed 's/"/\\"/g' | sed "s/'/\\'/g")
          echo "commit_message=$COMMIT_MSG" >> $GITHUB_OUTPUT

      - name: 快速部署到服务器
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "dist/*"
          target: "/home/<USER>/vue.wcy9.com/"
          strip_components: 1
          overwrite: true
          port: ${{ secrets.SSH_PORT }}
          timeout: 30s



      - name: 计算部署耗时
        id: duration
        if: always()
        run: |
          START_TIME=${{ steps.start-time.outputs.start }}
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - START_TIME))
          MINUTES=$((DURATION / 60))
          SECONDS=$((DURATION % 60))
          echo "duration=${MINUTES}分钟${SECONDS}秒" >> $GITHUB_OUTPUT

      # 🎉 优化部署通知
      - name: 部署成功通知
        if: success()
        run: |
          echo "🎉 Vue3项目部署成功！"
          echo "📊 构建大小: ${{ steps.build-info.outputs.size }}"
          echo "⏱️ 部署耗时: ${{ steps.duration.outputs.duration }}"
          echo "🌐 访问地址: https://vue.wcy9.com"
          echo "🔗 提交: ${{ github.sha }}"
          echo "👤 部署者: ${{ github.actor }}"

          # 发送详细通知
          curl -X POST "https://egg.wcy9.com/api/deploy/success" \
            -H "Content-Type: application/json" \
            -d "{
              \"project\": \"Vue3前端项目\",
              \"environment\": \"production\",
              \"branch\": \"${{ github.ref_name }}\",
              \"commitHash\": \"${{ github.sha }}\",
              \"commitMessage\": \"${{ steps.build-info.outputs.commit_message }}\",
              \"deployTime\": \"$(date '+%Y/%m/%d %H:%M:%S')\",
              \"deployDuration\": \"${{ steps.duration.outputs.duration }}\",
              \"deployedBy\": \"${{ github.actor }}\",
              \"buildSize\": \"${{ steps.build-info.outputs.size }}\",
              \"serverUrl\": \"https://vue.wcy9.com\"
            }" \
            --max-time 10 --retry 2 -s || echo "通知发送失败，但部署成功"

      - name: 部署失败通知
        if: failure()
        run: |
          echo "❌ Vue3项目部署失败"
          echo "🔗 查看日志: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
