<template>
  <a-row>
    <a-col span="5">
      <a-input
        v-model:value="correctRatioLow"
        placeholder="难度数"
        style="position: sticky; top: -2px"
      ></a-input>
      <a-input
        v-model:value="correctRatioHigh"
        placeholder="难度数"
        style="position: sticky; top: -2px"
      ></a-input>
      <a-input-number
        v-model:value="timulimit"
        placeholder="题目数"
        style="position: sticky; top: -2px"
      ></a-input-number>
      <div class="custom-list">
        <div v-for="item in newlist" :key="item.key" class="list-item">
          <a :href="`/fb/sy?kjid=${item.key}&type=syzc&id=123`" target="_blank">
            {{ '（' + item.sheet.name + '）-' + item.key }}
          </a>
        </div>
      </div>
      <p>
        <a href="/fb/nav2?type=gk"><el-button type="primary" plain>国考</el-button></a>
      </p>
      <p>
        <a href="/fb/nav2?type=gwy"><el-button type="primary" plain>公务员</el-button></a>
      </p>
      <p>
        <a href="/fb/nav2?type=zlfx"><el-button type="primary" plain>资料分析</el-button></a>
      </p>
      <p>
        <a href="/fb/nav1?type=gk"><el-button type="primary" plain>nav1</el-button></a>
      </p>
    </a-col>
    <a-col span="16">
      <div class="button-container">
        <a-space>
          <template v-if="!isLoading">
            <el-tree
              v-if="!isLoading"
              style="max-width: 600px; font-size: 26px"
              :data="menuItems"
              :props="defaultProps"
              node-key="id"
              :highlight-current="true"
              class="custom-tree"
              @node-click="handleNodeClick"
            />
            <a-spin v-else size="large" />
          </template>
          <p v-else><a-spin size="large" /></p>
        </a-space>
      </div>
    </a-col>
    <a-col span="4"></a-col>
  </a-row>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const menuItems = ref([]);
  const isLoading = ref(true);
  const type = ref('syzc');
  const timulimit = ref(10);
  const newlist = ref([]);
  const correctRatioLow = ref(0.1);
  const correctRatioHigh = ref(1);
  const defaultProps = {
    children: 'children',
    label: (data) => `${data.name}（${data.count || 0}）`,
  };

  const handleNodeClick = (node) => {
    console.log('点击节点:', node);
    // 可选打开链接
    // window.open(`/fb/gen?kjid=${node.id}&type=${type.value}`);
    openlink(node.id);
  };
  const getData = async () => {
    const url = '/egg/fbtree';
    const typex = route.query.type || type.value;
    const params = { type: typex };
    type.value = route.query.type || type.value;
    try {
      const response = await axios.get(url, { params });
      console.log(response.data);
      menuItems.value = response.data;
      isLoading.value = false;
    } catch (error) {
      console.error(error);
    }
  };

  const openlink = async (id) => {
    console.log(id);

    const url = '/egg/fbgettimu';
    let params = {
      type: type.value,
      id: id,
      limit: timulimit.value,
      gen: 1,
      correctRatioLow: correctRatioLow.value,
      correctRatioHigh: correctRatioHigh.value,
    };
    // console.log(params);
    try {
      const response = await axios.get(url, { params });

      let data = response.data;
      console.log(data);
      // window.open(
      //   `https://www.fenbi.com/spa/tiku/exam/practice/xingce/xingce/` +
      //     data.id +
      //     `/2?exerciseTimeMode=2`,
      // );
      window.open(`/fb/sy?kjid=${data.key}&type=${type.value}&id=123`);
      // setTimeout(() => {
      //   window.open(
      //     `https://www.fenbi.com/spa/tiku/exam/practice/xingce/xingce/${data.id}/2?exerciseTimeMode=2`,
      //   );
      // }, 100);
    } catch (error) {
      console.error(error);
    }
  };

  const renewgen = async () => {
    const url = '/egg/fbrenewgen';
    const typex = route.query.type || type.value;
    const params = { type: typex };
    type.value = route.query.type || type.value;
    try {
      const response = await axios.get(url, { params });
      // console.log(response.data);
      newlist.value = response.data.datas;
      // menuItems.value = response.data;
    } catch (error) {
      console.error(error);
    }
  };

  const updatehistory = async () => {
    try {
      await axios.get('/egg/fbhistory');
      console.log('update history');
    } catch (error) {
      console.error(error);
    }
  };

  onMounted(async () => {
    await updatehistory();
    await renewgen();
    await getData();
    //每隔 5 秒执行一次renewgen
    setInterval(renewgen, 2000);
  });
</script>

<style scoped>
  .button-container {
    text-align: center;
    margin-top: 20px;
  }

  a {
    text-decoration: none;
    margin-top: 20px;
  }

  .custom-list {
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;
    overflow: hidden;
  }

  .list-item {
    padding: 6px 12px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s;
    word-break: break-word;
    white-space: normal;
    line-height: 1.4;
  }

  .list-item:last-child {
    border-bottom: none;
  }

  .list-item:hover {
    background-color: #f5f5f5;
  }

  .list-item a {
    color: #1890ff;
    text-decoration: none;
    display: block;
    width: 100%;
  }

  .list-item a:hover {
    color: #40a9ff;
  }

  .custom-tree :deep(.el-tree-node) {
    margin-bottom: 8px;
  }

  .custom-tree :deep(.el-tree-node__content) {
    padding: 8px 0;
    line-height: 1.6;
  }

  .custom-tree :deep(.el-tree-node__children) {
    margin-top: 4px;
  }
</style>
