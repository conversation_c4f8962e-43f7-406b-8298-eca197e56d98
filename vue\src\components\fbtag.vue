<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import axios from 'axios';
  import { message } from 'ant-design-vue';

  export default defineComponent({
    name: 'TagComponent',
    props: {
      messageFromParent: Number,
      messageFromParent1: String,
    },
    setup(props) {
      const data = ref([]);
      const tag = ref(0);
      const handleTagClick = async () => {
        console.log('Tag clicked!', props.messageFromParent, props.messageFromParent1);
        const id = props.messageFromParent;
        const type = props.messageFromParent1;
        const url = '/egg/fbupdatetag';
        let params = {
          id: id,
          type: type,
          action: 'update',
        };
        console.log(params);
        try {
          const response = await axios.get(url, { params });
          data.value = response.data.data;
          await getData(1);
        } catch (error) {
          console.error(error);
          // Handle the error if needed
        }
      };

      const setzero = async () => {
        console.log('Tag clicked!', props.messageFromParent, props.messageFromParent1);
        const id = props.messageFromParent;
        const type = props.messageFromParent1;
        const url = '/egg/fbupdatetag';
        let params = {
          id: id,
          type: type,
          action: 'zero',
        };
        console.log(params);
        try {
          const response = await axios.get(url, { params });
          data.value = response.data.data;
          await getData(1);
        } catch (error) {
          console.error(error);
          // Handle the error if needed
        }
      };

      const getData = async (first = 0) => {
        const id = props.messageFromParent;
        const type = props.messageFromParent1;
        const url = '/egg/fbupdatetag';
        let params = {
          id: id,
          type: type,
        };
        console.log(params);
        try {
          const response = await axios.get(url, { params });
          data.value = response.data.data;
          tag.value = data.value.tag;
          if (first === 1) {
            message.success('' + tag.value);
          }
          // console.log(data.value);
        } catch (error) {
          console.error(error);
          // Handle the error if needed
        }
      };

      onMounted(() => {
        getData();
      });

      return {
        handleTagClick,
        getData,
        tag,
        setzero,
      };
    },
  });
</script>

<template>
  <p class="tag" @click="handleTagClick">打个胶{{ tag }}</p>
  <p class="tag" @click="setzero">清0胶{{ tag }}</p>
</template>

<style scoped></style>
