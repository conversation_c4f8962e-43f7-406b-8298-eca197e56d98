<!--aithink.vue-->
<template>
  <div class="aithink-container">
    <!-- 桌面端头部 -->
    <div class="header desktop-only">
      <h2>AI思考过程</h2>
      <div class="controls">
        <a-button @click="clearContent">清空</a-button>
        <a-button :type="autoScroll ? 'primary' : 'default'" @click="toggleAutoScroll">
          {{ autoScroll ? '关闭自动滚动' : '开启自动滚动' }}
        </a-button>
        <a-button :loading="isRefreshing" @click="refreshData">刷新数据</a-button>
        <a-button type="primary" style="background: linear-gradient(90deg, #ffb347, #ffcc33); color: #fff; border: none"
          @click="stopThinking">
          ⏹️ 停止思考
        </a-button>
      </div>
    </div>

    <!-- 移动端头部 -->
    <div class="mobile-header mobile-only">
      <div class="mobile-title">
        <h2>AI思考过程</h2>
        <a-button type="text" style="color: #1890ff; font-size: 18px" @click="showDrawer = true">
          ☰
        </a-button>
      </div>
    </div>

    <div class="content-area">
      <div class="status-bar">
        <span class="status idle"> 💤 等待思考数据... </span>
        <span class="connection-status" :class="{ connected: isConnected, disconnected: !isConnected }">
          {{ isConnected ? '🔗 已连接' : '❌ 未连接' }}
        </span>
      </div>

      <div ref="contentRef" class="thinking-content">
        <div v-if="thinkingContent.length === 0" class="empty-state">
          <p>👋 等待AI思考数据...</p>
          <p>连接成功后会自动显示思考过程</p>
        </div>

        <div v-else class="content-wrapper">
          <div v-for="pairWrapper in thinkingContent" :key="pairWrapper.id" class="thinking-pair">
            <div v-for="item in pairWrapper.items"
              :key="`${pairWrapper.id}-${item.type}-${item.timestamp}-${item._updateKey}`" class="thinking-item"
              :class="item.type">
              <div class="item-header">
                <span class="type-icon">
                  {{ item.type === 'thinking' ? '🧠' : '💭' }}
                </span>
                <span class="timestamp">{{ item.timestamp }}</span>
              </div>
              <Shuxue :content="item.displayedContent"
                :is-preformatted="item.type === 'answer' && isHtml(item.displayedContent)"
                :key="`${pairWrapper.id}-${item.type}-${item.timestamp}-${item._updateKey}`"
                :enable-cache="item.type === 'thinking'" :debounce-delay="item.type === 'answer' ? 100 : 300" />
            </div>
            <div v-if="pairWrapper.ended" class="end-marker">
              <a-divider>本次思考结束</a-divider>
            </div>
          </div>
          <!-- The sentinel div has been removed -->
        </div>
      </div>
      <!-- Scroll to Bottom Button -->
      <!-- The scroll-to-bottom-btn has been removed. -->
    </div>

    <div class="footer">
      <div class="stats">
        <span>思考片段: {{ thinkingContent.length }}</span>
        <span>总字符: {{ totalCharacters }}</span>
      </div>
    </div>

    <!-- 移动端抽屉菜单 -->
    <a-drawer title="操作菜单" placement="right" :visible="showDrawer" width="280" @close="showDrawer = false">
      <div class="drawer-content">
        <a-button block style="margin-bottom: 12px" @click="clearContent"> 清空内容 </a-button>
        <a-button block :type="autoScroll ? 'primary' : 'default'" style="margin-bottom: 12px"
          @click="toggleAutoScroll">
          {{ autoScroll ? '关闭自动滚动' : '开启自动滚动' }}
        </a-button>
        <a-button block :loading="isRefreshing" style="margin-bottom: 12px" @click="refreshData">
          刷新数据
        </a-button>
        <a-button block type="primary" style="
            background: linear-gradient(90deg, #ffb347, #ffcc33);
            color: #fff;
            border: none;
            margin-bottom: 12px;
          " @click="stopThinking">
          ⏹️ 停止思考
        </a-button>
        <a-divider />
        <div class="drawer-status">
          <p>
            <strong>连接状态:</strong>
            {{ isConnected ? '🔗 已连接' : '❌ 未连接' }}
          </p>
          <p><strong>思考片段:</strong> {{ thinkingContent.length }}</p>
          <p><strong>总字符:</strong> {{ totalCharacters }}</p>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script setup>
import Shuxue from '@/components/shuxue.vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import io from 'socket.io-client';
import { computed, nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
// The ArrowDownOutlined import has been removed.

// 响应式数据
const isConnected = ref(false);
const isRefreshing = ref(false);
const autoScroll = ref(true); // This is now the single source of truth for scrolling
const thinkingContent = ref([]);
const contentRef = ref(null);
const socket = ref(null);
const showDrawer = ref(false);
let streamEnded = ref(true);

// 打字机动画队列和状态
let typewriterQueue = [];
let isTypewriting = false;
let typewriterTimer = null;

function clearTypewriter() {
  typewriterQueue = [];
  isTypewriting = false;
  if (typewriterTimer) clearTimeout(typewriterTimer);
}

async function typeWriter(item, text, delay = 15) {
  for (let char of text) {
    await new Promise(res => {
      typewriterTimer = setTimeout(res, delay);
    });
    item.displayedContent += char;
  }
}

async function processTypewriterQueue() {
  if (isTypewriting || typewriterQueue.length === 0) return;
  isTypewriting = true;
  while (typewriterQueue.length > 0) {
    const { item, text, delay } = typewriterQueue.shift();
    await typeWriter(item, text, delay);
  }
  isTypewriting = false;
}

// Helper function to detect HTML content
const isHtml = (str) => {
  if (!str) return false;
  // A simple but effective regex to check for the presence of any HTML tag.
  const htmlTagRegex = /<([a-z][a-z0-9]*)\b[^>]*>/i;
  return htmlTagRegex.test(str);
};

// 计算属性
const totalCharacters = computed(() => {
  return thinkingContent.value.reduce((total, pairWrapper) => {
    return (
      total +
      pairWrapper.items.reduce(
        (pairTotal, item) => pairTotal + (item.fullContent || '').length,
        0,
      )
    );
  }, 0);
});

// Add or update content - Refactored for clarity and correctness
function addOrUpdateContent(type, newText, isHistory = false) {
  let lastPairWrapper =
    thinkingContent.value.length > 0
      ? thinkingContent.value[thinkingContent.value.length - 1]
      : null;

  // If a new thinking process starts, create a new pair wrapper.
  if (streamEnded.value && type === 'thinking') {
    lastPairWrapper = null;
    streamEnded.value = false;
  }

  // Find the target item to update, or determine if a new one is needed.
  let targetItem = lastPairWrapper
    ? lastPairWrapper.items.find((item) => item.type === type)
    : null;

  if (targetItem) {
    // --- UPDATE EXISTING ITEM ---
    if (type === 'thinking') {
      targetItem.fullContent += newText;
      typewriterQueue.push({ item: targetItem, text: newText, delay: 15 });
      processTypewriterQueue();
    } else {
      // type === 'answer' - 🚀 现在服务端也发送增量数据了！
      console.log('💭 Answer增量处理 - 新增内容:', newText.substring(0, 50) + '...');
      console.log('💭 Answer当前内容长度:', targetItem.fullContent.length);

      // 🔧 优化：累加新增内容并添加打字机效果
      targetItem.fullContent += newText;

      // 🎬 添加打字机动画效果
      typewriterQueue.push({ item: targetItem, text: newText, delay: 12 });
      processTypewriterQueue();

      console.log('✅ Answer增量累加完成，总长度:', targetItem.fullContent.length);
      console.log('🎬 Answer打字队列长度:', typewriterQueue.length);

      // 🔧 移除防抖，直接更新以支持打字机效果
      const itemIndex = lastPairWrapper.items.findIndex((item) => item.type === 'answer');
      if (itemIndex !== -1) {
        // 不需要创建新对象，直接更新现有对象以保持打字机动画
        lastPairWrapper.items[itemIndex].timestamp = new Date().toLocaleTimeString();
        lastPairWrapper.items[itemIndex]._updateKey = Date.now() + Math.random();
      }
    }
  } else {
    // --- CREATE NEW ITEM ---
    const newItem = {
      type,
      fullContent: newText,
      displayedContent: '', // 🔧 现在Answer也使用动画，初始为空
      timestamp: new Date().toLocaleTimeString(),
      _updateKey: Date.now() + Math.random(),
    };

    // Add to a new or existing pair wrapper
    if (!lastPairWrapper) {
      lastPairWrapper = { id: Date.now() + Math.random(), items: [newItem], ended: false };
      thinkingContent.value.push(lastPairWrapper);
    } else {
      lastPairWrapper.items.push(newItem);
    }

    // 🎬 为所有类型添加打字机动画
    if (type === 'thinking') {
      typewriterQueue.push({ item: newItem, text: newText, delay: 15 });
      processTypewriterQueue();
    } else if (type === 'answer') {
      typewriterQueue.push({ item: newItem, text: newText, delay: 12 });
      processTypewriterQueue();
    }
  }

  // Auto-scroll if enabled
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom(true);
    });
  }
}

// Socket连接
const connectSocket = () => {
  try {
    const thinkprocessSocket = io('/thinkprocess', {
      path: '/socket.io/',
      transports: ['websocket', 'polling'],
    });

    thinkprocessSocket.on('connect', () => {
      isConnected.value = true;
      message.success('Socket连接成功');
    });

    thinkprocessSocket.on('disconnect', () => {
      isConnected.value = false;
      message.warning('Socket连接断开');
    });

    thinkprocessSocket.on('thinking', (data) => {
      const content = data.content || data;
      addOrUpdateContent('thinking', content);
    });

    thinkprocessSocket.on('answer', (data) => {
      const content = data.content || data;
      addOrUpdateContent('answer', content);
    });

    thinkprocessSocket.on('end_of_stream', () => {
      console.log('🏁 接收到流结束标志');
      streamEnded.value = true;
      const lastPairWrapper =
        thinkingContent.value.length > 0
          ? thinkingContent.value[thinkingContent.value.length - 1]
          : null;
      if (lastPairWrapper) {
        lastPairWrapper.ended = true;

        // After showing the 'ended' marker, scroll to the bottom if auto-scroll is enabled.
        if (autoScroll.value) {
          nextTick(() => {
            scrollToBottom(true);
          });
        }
      }
    });

    thinkprocessSocket.on('connect_error', (error) => {
      message.error(`Socket连接错误: ${error.message}`);
    });

    socket.value = thinkprocessSocket;
  } catch (error) {
    message.error('Socket连接失败');
  }
};

// 获取历史数据
const fetchHistoryData = async () => {
  try {
    const response = await fetch('/egg/thinkprocess');
    const data = await response.json();

    if (data.success && data.thinking) {
      console.log('📚 获取到历史思考数据');
      addOrUpdateContent('thinking', data.thinking, true);
    }

    if (data.success && data.answer) {
      console.log('📚 获取到历史答案数据');
      addOrUpdateContent('answer', data.answer, true);
    }
  } catch (error) {
    console.error('❌ 获取历史数据失败:', error);
  }
};

// 刷新数据
const refreshData = async () => {
  isRefreshing.value = true;
  thinkingContent.value = []; // 清空之前的内容
  clearTypewriter(); // 清空打字机动画队列

  try {
    await fetchHistoryData();
    message.success('数据已刷新');
  } catch (error) {
    console.error('刷新数据失败:', error);
    message.error('刷新数据失败');
  } finally {
    isRefreshing.value = false;
  }
};

// 停止思考美化方法
const stopThinking = async () => {
  try {
    const res = await axios.post('/egg/thinkprocess/stop', { stop: true });
    const data = res.data;
    if (data.success) {
      message.success('已发送停止思考美化指令');
    } else {
      message.error('停止失败：' + (data.message || '未知错误'));
    }
  } catch (e) {
    message.error('请求失败：' + e.message);
  }
};

// 清空内容
function clearContent() {
  thinkingContent.value = [];
  clearTypewriter(); // 清空打字机动画队列
  message.success('内容已清空');
}

// Toggle auto-scroll button logic
function toggleAutoScroll() {
  autoScroll.value = !autoScroll.value;
  if (autoScroll.value) {
    scrollToBottom(); // Scroll to bottom when it's turned back on.
  }
}

// This function now just scrolls to the bottom. 'isSmooth' param added.
function scrollToBottom(isSmooth = true) {
  if (contentRef.value) {
    contentRef.value.scrollTo({
      top: contentRef.value.scrollHeight,
      behavior: isSmooth ? 'smooth' : 'auto',
    });
  }
}

// The handleScroll function has been removed.

// 生命周期钩子
onMounted(async () => {
  await fetchHistoryData(); // 启动时也获取历史数据
  connectSocket();
});

onBeforeUnmount(() => {
  if (socket.value) socket.value.disconnect();
  clearTypewriter(); // 组件卸载时清空打字机动画队列
  console.log('🧹 清理所有打字机效果资源');
});
</script>

<style scoped>
.aithink-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  /* 保证 padding 不会撑开容器高度 */
}

/* 桌面端样式 */
.desktop-only {
  display: block;
}

.mobile-only {
  display: none;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.header h2 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
}

.controls {
  display: flex;
  gap: 10px;
}

/* 移动端样式 */
.mobile-header {
  padding: 10px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.mobile-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mobile-title h2 {
  margin: 0;
  color: #1890ff;
  font-size: 20px;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* position: relative; has been removed */
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 15px;
}

.status {
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
}

.status.thinking {
  background: #e6f7ff;
  color: #1890ff;
}

.status.idle {
  background: #f6ffed;
  color: #52c41a;
}

.connection-status {
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 20px;
}

.connection-status.connected {
  background: #f6ffed;
  color: #52c41a;
}

.connection-status.disconnected {
  background: #fff2f0;
  color: #ff4d4f;
}

.thinking-content {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
  overflow-y: auto;
  /* min-height: 400px; 移除最小高度，让 flex: 1 完全控制，防止其在小屏幕上撑开布局 */
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  font-size: 16px;
  text-align: center;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.thinking-item {
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid;
  background: #fafafa;
}

.thinking-item.thinking {
  border-left-color: #1890ff;
  background: #f0f8ff;
  width: 100%;
  /* 让思考内容占满整个容器宽度 */
  max-width: 100%;
}

.thinking-item.answer {
  border-left-color: #52c41a;
  background: #f6ffed;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
  color: #666;
}

.type-icon {
  font-size: 16px;
}

.timestamp {
  color: #999;
}

.footer {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.stats {
  display: flex;
  justify-content: space-between;
  color: #666;
  font-size: 14px;
}

/* 抽屉样式 */
.drawer-content {
  display: flex;
  flex-direction: column;
}

.drawer-status {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.drawer-status p {
  margin: 8px 0;
  font-size: 14px;
}

/* 滚动条样式 */
.thinking-content::-webkit-scrollbar {
  width: 8px;
}

.thinking-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.thinking-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.thinking-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.thinking-pair {
  border-bottom: 1px dashed #ccc;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.thinking-pair:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.end-marker {
  margin-top: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .aithink-container {
    padding: 10px;
    height: 100vh;
  }

  .desktop-only {
    display: none;
  }

  .mobile-only {
    display: block;
  }

  .status-bar {
    flex-direction: row;
    gap: 10px;
    align-items: center;
    justify-content: space-between;
  }

  .thinking-content {
    padding: 15px;
    /* min-height: 300px; */
  }

  .stats {
    flex-direction: row;
    gap: 20px;
    justify-content: space-between;
  }

  .thinking-item {
    padding: 12px;
  }

  .item-header {
    font-size: 11px;
  }

  .type-icon {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .aithink-container {
    padding: 5px;
  }

  .mobile-title h2 {
    font-size: 18px;
  }

  .thinking-content {
    padding: 10px;
    /* min-height: 250px; */
  }

  .thinking-item {
    padding: 10px;
  }

  .status-bar {
    padding: 8px 12px;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
  }

  .status,
  .connection-status {
    font-size: 12px;
    padding: 3px 8px;
  }

  .stats {
    flex-direction: row;
    gap: 15px;
    justify-content: space-between;
    font-size: 12px;
  }
}

/* The .sentinel style has been removed */
/* The .scroll-to-bottom-btn style has been removed. */

/* 
  .typewriter-container, .typewriter, @keyframes have been removed 
  as the animation is now handled by JavaScript for a smoother,
  uninterrupted experience with dynamic content.
*/
</style>
