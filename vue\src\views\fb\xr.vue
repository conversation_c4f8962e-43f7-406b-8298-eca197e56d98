<script setup>
  import { onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const imglist = ref([]);
  const currentIndex = ref(0);

  // 预加载图片
  const preloadImages = (images) => {
    images.forEach((src) => {
      const img = new Image();
      img.src = src; // 浏览器会预先加载这些图片资源
    });
  };

  // 获取数据并预加载图片
  const getData = async () => {
    const id = route.query.id || 16697;
    const url = '/egghk/xrpluspageinfo';
    let params = { id };

    try {
      const response = await axios.get(url, { params });
      if (!response.data || response.data.length === 0) {
        console.error('No data received. Please check the server response.');
        return;
      }

      imglist.value = response.data; // 更新图片列表
      preloadImages(imglist.value); // 预加载图片
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  // 显示上一张图片
  const showPrev = () => {
    currentIndex.value = (currentIndex.value - 1 + imglist.value.length) % imglist.value.length;
  };

  // 显示下一张图片
  const showNext = () => {
    currentIndex.value = (currentIndex.value + 1) % imglist.value.length;
  };

  onMounted(async () => {
    await getData();
  });
</script>

<template>
  <a-row type="flex">
    <!-- 左边按钮 -->
    <a-col :lg="5" :sm="1" :xs="1" :md="1" @click="showPrev">
      <div class="lb">上一张</div>
    </a-col>

    <!-- 图片显示区域 -->
    <a-col :lg="14" :sm="22" :xs="22" :md="22" @click="showNext">
      <div class="wp">
        <img v-if="imglist.length > 0" class="full-width-image" :src="imglist[currentIndex]" />
      </div>
    </a-col>

    <!-- 右边按钮 -->
    <a-col :lg="5" :sm="1" :xs="1" :md="1" @click="showNext">
      <div class="rb">下一张</div>
    </a-col>
  </a-row>
</template>

<style scoped>
  /* 确保整个页面的溢出被隐藏 */
  html,
  body {
    margin: 0;
    padding: 0;
    overflow: hidden; /* 禁止滚动条 */
  }

  .wp {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh; /* 确保占满视口高度 */
  }

  .full-width-image {
    width: 100%; /* 宽度适应父容器 */
    height: 100%; /* 高度适应父容器 */
    object-fit: contain; /* 保持图片完整可见 */
  }

  .lb,
  .rb {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 18px;
    cursor: pointer;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
  }
</style>
