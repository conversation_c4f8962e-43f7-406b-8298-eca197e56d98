// src/store/timu.js
import { defineStore } from 'pinia';
import axios from 'axios';
import { message } from 'ant-design-vue';

export const useTimuStore = defineStore('timu', {
  state: () => ({
    list: [],
    currentPage: 1,
    total: 0,
    perPage: 1,
    isLoading: false,
    currentTimuForEdit: null,
    // ... any other state from sy.vue related to data
  }),
  actions: {
    async fetchTimuList(params) {
      this.isLoading = true;
      try {
        const response = await axios.get('/egg/fbtimu', { params });
        this.list = response.data.data;
        this.total = response.data.pagetotal[0].total;
      } catch (error) {
        console.error('Failed to fetch timu list:', error);
        message.error('题目列表加载失败');
      } finally {
        this.isLoading = false;
      }
    },
    async fetchTimuForEdit(biao, timuid) {
      this.isLoading = true;
      try {
        const url = '/egg/updatetimu';
        const params = { biao, timuid };
        const response = await axios.get(url, { params });
        this.currentTimuForEdit = response.data.data;
      } catch (error) {
        console.error('Failed to fetch timu for edit:', error);
        message.error('加载待编辑题目失败');
      } finally {
        this.isLoading = false;
      }
    },
    async updateTimu(payload) {
      try {
        const { biao, timuid, ...up } = payload;
        const url = '/egg/updatetimu';
        const params = { biao, timuid };
        const response = await axios.post(url, up, { params });
        if (+response.data.x['affectedRows'] !== 0) {
          message.success('更新成功');
          this.currentTimuForEdit = response.data.data;
        } else {
          message.error('更新失败');
        }
      } catch (error) {
        console.error('Failed to update timu:', error);
        message.error('更新题目时发生错误');
      }
    },
  },
});
