<template>
  <a-row>
    <a-col span="5">
      <a-input
        v-model:value="correctRatioLow"
        placeholder="难度数"
        style="position: sticky; top: -2px"
      ></a-input>
      <a-input
        v-model:value="correctRatioHigh"
        placeholder="难度数"
        style="position: sticky; top: -2px"
      ></a-input>
      <a-input-number
        v-model:value="timulimit"
        placeholder="题目数"
        style="position: sticky; top: -2px"
      ></a-input-number>
      <p>
        <a href="/fb/sy?biao=gzslwwb1&type=gz&id=2&o=1&f=2" target="_blank"
          ><el-button type="primary" plain>高照</el-button></a
        >
      </p>
      <p>
        <a href="/fb/sy?biao=gzyy&type=gz&id=11&o=1&f=2" target="_blank"
          ><el-button type="primary" plain>高照言语</el-button></a
        >
      </p>
      <p>
        <a href="/fb/sy?biao=hs22sl&type=gz&id=22&o=1&f=2" target="_blank"
          ><el-button type="primary" plain>花生22</el-button></a
        >
      </p>
      <p>
        <a href="/fb/sy?biao=hspdyd&type=gz&id=33&o=1&f=2" target="_blank"
          ><el-button type="primary" plain>花生片段阅读</el-button></a
        >
      </p>
      <p>
        <a href="/fb/sy?biao=hsljpd&type=gz&id=600&o=1&f=2" target="_blank"
          ><el-button type="primary" plain>花生逻辑判断</el-button></a
        >
      </p>
      <p>
        <a href="/fb/sy?biao=hsydsl&type=gz&id=21&o=1&f=2" target="_blank"
          ><el-button type="primary" plain>袁东数量</el-button></a
        >
      </p>
      <p>
        <a href="/fb/sy?biao=hsydpd&type=gz&id=22&o=1&f=2" target="_blank"
          ><el-button type="primary" plain>袁东推理</el-button></a
        >
      </p>
      <a-list bordered :data-source="newlist">
        <template #renderItem="{ item }">
          <a :href="`/fb/sy?kjid=${item.key}&type=syzc&id=123`" target="_blank">
            <a-list-item> {{ '（' + item.sheet.name + '）-' + item.key }}</a-list-item></a
          >
        </template>
      </a-list>
      <p>
        <a href="/fb/nav2?type=gk"><el-button type="primary" plain>国考</el-button></a>
      </p>
      <p>
        <a href="/fb/nav2?type=gwy"><el-button type="primary" plain>公务员</el-button></a>
      </p>
      <p>
        <a href="/fb/nav2?type=zlfx"><el-button type="primary" plain>资料分析</el-button></a>
      </p>
      <p>
        <a href="/fb/nav1?type=gk"><el-button type="primary" plain>nav1</el-button></a>
      </p>
    </a-col>
    <a-col span="16">
      <div class="button-container">
        <a-space>
          <template v-if="!isLoading">
            <el-tree
              v-if="!isLoading"
              style="max-width: 600px; font-size: 26px"
              :data="menuItems"
              :props="defaultProps"
              node-key="id"
              :highlight-current="true"
              class="custom-tree"
              @node-click="handleNodeClick"
            />
            <a-spin v-else size="large" />
          </template>
          <p v-else><a-spin size="large" /></p>
        </a-space>
      </div>
    </a-col>
    <a-col span="4"></a-col>
  </a-row>
</template>

<script setup>
// 🔧 移除：不再需要手动管理主题
// import { useThemeStore } from '@/store/theme';
import axios from 'axios';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const menuItems = ref([]);
const isLoading = ref(true);
const type = ref('syzc');
const timulimit = ref(1);
const newlist = ref([]);
const correctRatioLow = ref(0.1);
const correctRatioHigh = ref(0.99);
const defaultProps = {
  children: 'children',
  label: (data) => `${data.name}（${data.answerCount || 0}/${data.count || 0}）`,
};

const handleNodeClick = (node) => {
  console.log('点击节点:', node);
  // 可选打开链接
  // window.open(`/fb/gen?kjid=${node.id}&type=${type.value}`);
  openlink(node.id);
};
const getData = async () => {
  const url = '/egg/fbtree';
  const typex = route.query.type || type.value;
  const params = { type: typex };
  type.value = route.query.type || type.value;
  try {
    const response = await axios.get(url, { params });
    console.log(response.data);
    menuItems.value = response.data;
    isLoading.value = false;
  } catch (error) {
    console.error(error);
  }
};

const openlink = async (id) => {
  console.log(id);

  const url = '/egg/fbgettimu';
  let params = {
    type: type.value,
    id: id,
    limit: timulimit.value,
    gen: 1,
    correctRatioLow: correctRatioLow.value,
    correctRatioHigh: correctRatioHigh.value,
  };
  // console.log(params);
  try {
    // const response = await axios.get(url, { params });

    // let data = response.data;
    // console.log(data);
    // window.open(
    //   `https://www.fenbi.com/spa/tiku/exam/practice/xingce/xingce/` +
    //     data.id +
    //     `/2?exerciseTimeMode=2`,
    // );
    window.open(`/fb/sy?biao=fbsy&type=syzc&id=${id}`);
    // setTimeout(() => {
    //   window.open(
    //     `https://www.fenbi.com/spa/tiku/exam/practice/xingce/xingce/${data.id}/2?exerciseTimeMode=2`,
    //   );
    // }, 100);
  } catch (error) {
    console.error(error);
  }
};

const renewgen = async () => {
  const url = '/egg/fbrenewgen';
  const typex = route.query.type || type.value;
  const params = { type: typex };
  type.value = route.query.type || type.value;
  try {
    const response = await axios.get(url, { params });
    // console.log(response.data);
    newlist.value = response.data.datas;
    // menuItems.value = response.data;
  } catch (error) {
    console.error(error);
  }
};

const updatehistory = async () => {
  // try {
  //   await axios.get('/egg/fbhistory');
  //   console.log('update history');
  // } catch (error) {
  //   console.error(error);
  // }
};

// 🔧 移除：不再需要主题 store
// const themeStore = useThemeStore();

onMounted(async () => {
  // 🔧 简化：App.vue 会自动处理主题，这里不需要手动设置
  window.document.title = 'nav3';

  await updatehistory();
  await renewgen();
  await getData();
  //每隔 5 秒执行一次renewgen
  setInterval(renewgen, 6200);
});
</script>

<style scoped>
/* nav3.vue 专用样式 */
.button-container {
  text-align: center;
  margin-top: 20px;
}

a {
  text-decoration: none;
  margin-top: 20px;
}

.custom-tree :deep(.el-tree-node) {
  margin-bottom: 8px;
}

.custom-tree :deep(.el-tree-node__content) {
  padding: 8px 0;
  line-height: 1.6;
}

.custom-tree :deep(.el-tree-node__children) {
  margin-top: 4px;
}

/* 确保nav3页面使用纯白主题 */
.ant-row {
  background: var(--theme-bg);
  color: var(--theme-color);
  min-height: 100vh;
  padding: 20px;
}

.ant-col {
  background: transparent;
}

/* 输入框样式 */
.ant-input,
.ant-input-number {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-number:focus {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 列表样式 */
.ant-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-list-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.ant-list-item:hover {
  background-color: #f5f5f5;
}

.ant-list-item:last-child {
  border-bottom: none;
}

/* 树形控件样式 */
.custom-tree {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.ant-spin {
  color: #1677ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-row {
    padding: 10px;
  }

  .custom-tree {
    font-size: 22px;
    padding: 12px;
  }

  .ant-col[span='5'] {
    margin-bottom: 20px;
  }
}
</style>
