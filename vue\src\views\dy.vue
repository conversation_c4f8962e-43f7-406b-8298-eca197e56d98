<template>
  <div class="content">
    <div class="main">
      <a-space direction="vertical" align="center">
        <h1>抖音地址提取</h1>
        <a-input
          v-model:value="otext"
          placeholder="复制到这里"
          style="width: 220px"
          allow-clear
        ></a-input>
        <div>
          <a :href="generatedLink" target="_blank" style="word-wrap: break-word">{{
            generatedLink
          }}</a>
        </div>
      </a-space>
    </div>
  </div>
</template>

<script>
  import { ref, computed } from 'vue';
  import Qrcode from 'vue-qrcode';
  import { message } from 'ant-design-vue';
  export default {
    name: 'DownloadLink',
    components: {
      Qrcode,
    },
    setup() {
      window.document.title = '抖音地址提取';
      const downloadUrl = ref('');
      const otext = ref('');
      const qrcodeColor = ref({ dark: '#000', light: '#fff' });

      const generatedLink = computed(() => {
        // const baseUrl = 'https://re.101616.xyz/';
        // console.log(downloadUrl.value);
        const urlPattern = /https?:\/\/[^\s]+/;
        const match = otext.value.match(urlPattern);

        if (match) {
          console.log('找到的 URL：', match[0]);
          return match[0];
        } else {
          console.log('没有找到 URL');
        }
      });
      function copyToClipboard(text) {
        try {
          navigator.clipboard.writeText(text);
          message.success('复制成功!');
        } catch (err) {
          console.error('Failed to copy text: ', err);
          message.warning('复制失败!');
        }
      }
      return {
        otext,
        downloadUrl,
        generatedLink,
        qrcodeColor,
        copyToClipboard,
      };
    },
  };
</script>

<style scoped>
  @media screen and (max-width: 200px) {
    .main {
      width: 250px;
    }
  }
  .main {
    max-width: 950px;
    padding: 19px 29px 29px;
    margin: 62px auto 62px;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
