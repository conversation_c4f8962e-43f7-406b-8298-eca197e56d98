<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div v-show="showTop" class="top">
          <a-button @click="increaseFontSize">增大字体</a-button>
          <a-button @click="togglean(data.referenceAnswer)">显示答案</a-button>
          <a-button>{{ total }}</a-button>
          <a-button @click="toggleTop">隐藏顶部内容</a-button>
          <a-input v-model:value="biaoming" placeholder="表名" @change="getData"></a-input>
          <el-pagination
            v-model:current-page="current1"
            v-model:page-size="pageSize3"
            show-quick-jumper
            :page-sizes="[1, 4, 10, 40, 50, 62, 100, 200, 300, 400, 2000]"
            layout="sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="getData"
            @current-change="getData"
          />
        </div>
        <div v-for="(item, index) in data" :key="index">
          <div v-if="isLoading">Loading...</div>
          <div v-if="!isLoading">
            <div v-html="item.content"></div>
            <div class="item">
              <a-row>
                <a-col :span="24">
                  <p class="an_a" v-html="item.answerone"></p>
                </a-col>
                <a-col :span="24"><p class="an_b" v-html="item.answertwo"></p> </a-col>
                <a-col :span="24"><p class="an_c" v-html="item.answerthree"></p> </a-col>
                <a-col :span="24"><p class="an_d" v-html="item.answerfour"></p> </a-col>
              </a-row>
            </div>
            <p style="color: #00ff05" @click="toggleTop">================</p>
            <div v-show="showContent" class="answer">
              <div>
                <span style="color: blue">{{ item.choice }}</span>
                {{ item.source }}{{ item.createdTime }}
              </div>
              <br />
              <div>
                <div v-html="item.solution"></div>
              </div>
              <p>================</p>
            </div>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  const data = ref([]);
  const isLoading = ref(true);
  const current1 = ref(1);
  const showContent = ref(false);
  const pagetotal = ref(20);
  const pageSize3 = ref(4);
  const showTop = ref(true);
  const total = ref(210);
  const isdangerarr = ref([false, false, false, false]);
  const route = useRoute();
  const fontStyle = ref(16);
  const biaoming = ref('');
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const increaseFontSize = () => {
    fontStyle.value += 2;
    updateFontSize();
  };
  const updateFontSize = () => {
    const wpElement = document.querySelector('.wp');
    wpElement.style.fontSize = `${fontStyle.value}px`;
  };
  const handleKeyDown = async (event) => {
    if (event.key === 'ArrowLeft' || event.key === 'q') {
      if (current1.value > 1) {
        current1.value -= 1;
        await getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);
          await ansblack();
          showContent.value = false;
        }
      }
    } else if (event.key === 'ArrowRight' || event.key === 'e') {
      if (current1.value < 10000) {
        current1.value += 1;
        await getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);
          await ansblack();
          showContent.value = false;
        }
      }
    }
  };

  const right = () => {
    if (current1.value < 10000) {
      current1.value += 1;
      getData();
      ansblack();

      if (showContent.value) {
        isDanger(false);

        showContent.value = false;
      }
    }
  };

  const left = () => {
    if (current1.value > 1) {
      current1.value -= 1;
      getData();
      ansblack();

      if (showContent.value) {
        isDanger(false);

        showContent.value = false;
      }
    }
  };
  const toggleContent = async (event) => {
    if (
      event.key === ' ' ||
      event.key === 'Spacebar' ||
      event.key === 'w' ||
      event.key === 'ArrowUp' ||
      event.key === 'ArrowDown'
    ) {
      if (showContent.value) {
        // isDanger(false);
        showContent.value = false;
        await ansblack();
      } else {
        // ansred();
        // const showAnsButton = document.querySelector('.showans');
        // showAnsButton.click();
        showContent.value = true;
        await ansred();
      }
    }
  };

  const ansred = async () => {
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'red';
      }
    }
  };

  const ansblack = async () => {
    let clen = 0;
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'black';
      }
    }
  };

  const togglean = async (answer) => {
    if (showContent.value) {
      await ansblack();
      isDanger(false);
      showContent.value = false;
    } else {
      await ansblack();
      await ansred();
      isDanger(answer);
      showContent.value = true;
    }
  };

  const pushans = async (answer) => {
    // const url = '/egg/minchoice';
    // data.value.choice = answer;
    // const response = await axios.post(url, data.value);
    // console.log(response);
  };

  const isDanger = (answer) => {
    if (data.value && data.value.referenceAnswer === answer && answer !== false) {
      switch (answer) {
        case 'A':
          isdangerarr.value[0] = true;
          break;
        case 'B':
          isdangerarr.value[1] = true;
          break;
        case 'C':
          isdangerarr.value[2] = true;
          break;
        case 'D':
          isdangerarr.value[3] = true;
          break;
      }
      return true;
    } else if (data.value && answer === false) {
      isdangerarr.value.fill(false);
    }
    return false;
  };

  const getData = async () => {
    const per = route.query.per || pageSize3.value;
    const a = route.query.a || false;
    const id = route.query.id || 48905;
    const page = route.query.page || current1.value;
    const type = route.query.type || 'gwy';
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/fbyy';
    let params = {
      per: per,
      page: page,
      id: id,
      type: type,
      isfive: 1,
      biao: biaoming.value,
    };
    console.log(params);
    await ansblack();
    try {
      const response = await axios.get(url, { params });
      // if (response.data.length === 0) {
      //   await getData();
      //   return;
      // }
      if (a) {
        showContent.value = true;
      }
      data.value = response.data;
      // console.log(data.value);
      for (let item in data.value) {
        data.value[item].content = data.value[item].content.replace(
          '>',
          `>${page * per - per + 1 + +item}.`,
        );

        data.value[item].answerone = 'A.' + data.value[item].answerone;
        data.value[item].answertwo = 'B.' + data.value[item].answertwo;
        data.value[item].answerthree = 'C.' + data.value[item].answerthree;
        data.value[item].answerfour = 'D.' + data.value[item].answerfour;
        if (!data.value[item].solution.match(/<p>A/g)) {
          data.value[item].solution = data.value[item].solution.replace(/A项/g, '<br/><br/>A项');
        }
        if (!data.value[item].solution.match(/<p>B/g)) {
          data.value[item].solution = data.value[item].solution.replace(/B项/g, '<br/><br/>B项');
        }
        if (!data.value[item].solution.match(/<p>C/g)) {
          data.value[item].solution = data.value[item].solution.replace(/C项/g, '<br/><br/>C项');
        }
        if (!data.value[item].solution.match(/<p>D/g)) {
          data.value[item].solution = data.value[item].solution.replace(/D项/g, '<br/><br/>D项');
        }
      }
      // ansblack();
      // isDanger(false);

      showContent.value = false;
      isLoading.value = false;
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', toggleContent);
    getData();
    window.addEventListener('keydown', handleKeyDown);
    updateFontSize();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
  });
</script>

<style scoped>
  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }
</style>
