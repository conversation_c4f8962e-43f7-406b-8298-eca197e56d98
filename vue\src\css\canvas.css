/* canvas.css - 画布相关样式 */

/* 画布提示样式 */
.canvas-tip {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 12px;
  line-height: 1.4;
  z-index: 1000;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.canvas-tip-close {
  position: absolute;
  top: 5px;
  right: 8px;
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.canvas-tip-close:hover {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

/* 画布容器样式 */
#drawingCanvas {
  position: absolute;
  background-color: rgba(255, 255, 255, 0);
  touch-action: none;
  user-select: none;
  z-index: 10;
}

#indicatorCanvas {
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  user-select: none;
  z-index: 11;
}

/* 画布按钮样式 */
.canvas-controls {
  position: fixed;
  top: 60px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 1001;
}

.canvas-controls .ant-btn {
  min-width: 80px;
  font-size: 12px;
}

/* 画布工具栏样式 */
.canvas-toolbar {
  position: fixed;
  bottom: 80px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1001;
}

.canvas-toolbar .ant-input-number {
  width: 80px;
}

/* 响应式画布样式 */
@media screen and (max-width: 768px) {
  .canvas-tip {
    position: fixed;
    top: 5px;
    left: 5px;
    right: 5px;
    max-width: none;
    font-size: 11px;
  }

  .canvas-controls {
    top: auto;
    bottom: 120px;
    right: 5px;
    flex-direction: row;
    flex-wrap: wrap;
    max-width: 200px;
  }

  .canvas-controls .ant-btn {
    min-width: 60px;
    font-size: 10px;
    padding: 2px 6px;
  }

  .canvas-toolbar {
    bottom: 60px;
    right: 5px;
    left: 5px;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
  }

  .canvas-toolbar .ant-input-number {
    width: 60px;
  }
}

/* 画布全屏模式 */
.canvas-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9998;
  background: rgba(255, 255, 255, 0.95);
}

.canvas-fullscreen #drawingCanvas,
.canvas-fullscreen #indicatorCanvas {
  width: 100vw !important;
  height: 100vh !important;
}

/* 画布加载状态 */
.canvas-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1002;
}

/* 画布错误状态 */
.canvas-error {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 20px;
  border-radius: 8px;
  max-width: 400px;
  text-align: center;
  z-index: 1002;
}

/* 画布手势提示 */
.canvas-gesture-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  opacity: 0.8;
  z-index: 1001;
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.8; }
}

/* 画布笔刷预览 */
.brush-preview {
  position: fixed;
  pointer-events: none;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.1);
  z-index: 1003;
  transition: all 0.1s ease;
}

.brush-preview.erase-mode {
  border-color: rgba(255, 0, 0, 0.5);
  background: rgba(255, 0, 0, 0.1);
}

/* 画布保存状态指示器 */
.canvas-save-indicator {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #52c41a;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1002;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.canvas-save-indicator.show {
  opacity: 1;
}

.canvas-save-indicator.error {
  background: #ff4d4f;
}

/* 画布快捷键提示 */
.canvas-shortcuts {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 6px;
  font-size: 11px;
  line-height: 1.4;
  max-width: 200px;
  z-index: 1001;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.canvas-shortcuts:hover {
  opacity: 1;
}

.canvas-shortcuts h4 {
  margin: 0 0 5px 0;
  font-size: 12px;
  font-weight: bold;
}

.canvas-shortcuts ul {
  margin: 0;
  padding-left: 15px;
}

.canvas-shortcuts li {
  margin: 2px 0;
}

/* 隐藏画布时的样式 */
.canvas-hidden #drawingCanvas,
.canvas-hidden #indicatorCanvas,
.canvas-hidden .canvas-tip,
.canvas-hidden .canvas-controls,
.canvas-hidden .canvas-toolbar {
  display: none !important;
}
