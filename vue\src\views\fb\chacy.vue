<template>
  <a-row>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" @click="left">
      <div class="lb" style=""></div>
    </a-col>
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center">
      <span>
        <a-input-number id="inputNumber" v-model:value="perpage" :min="1" @change="getData" />
        <a-button @click="left">p</a-button>
        <a-input-number id="inputNumber" v-model:value="current_page" :min="1" @change="getData" />
        <a-button @click="right">n</a-button>
        <a-input v-model:value="cy" @change="getData"></a-input
      ></span>

      <div v-for="(item, index) in data">
        <div class="maintimu" style="color: black; font-size: 18px" v-html="item.content"></div>
        <div class="item" style="color: black; font-size: 18px">
          <p
            class="an_a"
            :style="{
              color: item.answer === 'A' ? 'red' : '',
            }"
            v-html="item.answerone"
          ></p>
          <p
            class="an_b"
            :style="{ color: item.answer === 'B' ? 'red' : '' }"
            v-html="item.answertwo"
          ></p>
          <p
            class="an_c"
            :style="{ color: item.answer === 'C' ? 'red' : '' }"
            v-html="item.answerthree"
          ></p>
          <p
            class="an_d"
            :style="{ color: item.answer === 'D' ? 'red' : '' }"
            v-html="item.answerfour"
          ></p>
        </div>
        <p>================================</p>
        <div class="answer" style="color: red; font-size: 18px">
          <div class="ansinfo">{{ item.source }}{{ item.createdTime }}</div>
          <br />
          <div>
            <div v-html="item.solution"></div>
          </div>
          <div></div>
          <p>==================</p>
        </div>
      </div>
    </a-col>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" @click="right">
      <div class="rb" style=""></div>
    </a-col>
  </a-row>
</template>
<script setup>
  import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  const route = useRoute();
  const activeKey = ref([]);
  const text = ref('wcnm');
  const data = ref([]);
  const timusdata = ref([]);
  const jieshitext = ref('');
  const cy = ref('');
  const current_page = ref(1);
  const perpage = ref(30);
  const postjieshi = async (text, id) => {
    const url = '/egg/fbgetmncy';
    text = `【解释】` + text;
    let params = {
      biao: 'fbmncy1',
      text: text,
      id: id,
      jiexi: '2',
    };
    try {
      const response = await axios.get(url, { params });
      console.log(response.data);
      console.log(text, id);
      jieshitext.value = null;
      await getData();
    } catch (error) {
      console.error(error);
    }
  };
  const open_count = ref(0);

  const updateValue = async (delta) => {
    open_count.value = 0;
    const newValue = +current_page.value + delta;
    if (newValue >= 1 && newValue <= 100000) {
      current_page.value = newValue;
      await getData();
    }
  };

  const right = async () => {
    await updateValue(1);
  };

  const left = async () => {
    await updateValue(-1);
  };

  const getData = async () => {
    const z = route.query.z || 0;
    const biao = route.query.biao || 'fbgwy';
    const url = '/egg/fbgetcy';
    let params = {
      biao: biao,
      cy: cy.value,
      per: perpage.value,
      page: current_page.value,
      z: z,
    };
    console.log(params);
    try {
      const response = await axios.get(url, { params });
      data.value = response.data;
      let x = [];
      for (let item in data.value) {
        data.value[item].content = data.value[item].content.replace(
          /<p>/,
          '<p>' + (+item + 1) + '.',
        );
        if (!data.value[item].solution.match(/<p>A/g)) {
          data.value[item].solution = data.value[item].solution.replace(/A项/g, '<br/>A项');
        }
        if (!data.value[item].solution.match(/<p>B/g)) {
          data.value[item].solution = data.value[item].solution.replace(/B项/g, '<br/>B项');
        }
        if (!data.value[item].solution.match(/<p>C/g)) {
          data.value[item].solution = data.value[item].solution.replace(/C项/g, '<br/>C项');
        }
        if (!data.value[item].solution.match(/<p>D/g)) {
          data.value[item].solution = data.value[item].solution.replace(/D项/g, '<br/>D项');
        }
        if (data.value[item].answerone.match(cy.value) && data.value[item].answer === 'A') {
          x.push(data.value[item]);
        }
        if (data.value[item].answertwo.match(cy.value) && data.value[item].answer === 'B') {
          x.push(data.value[item]);
        }
        if (data.value[item].answerthree.match(cy.value) && data.value[item].answer === 'C') {
          x.push(data.value[item]);
        }
        if (data.value[item].answerfour.match(cy.value) && data.value[item].answer === 'D') {
          x.push(data.value[item]);
        }
      }
      if (+z === 1) {
        data.value = x;
      }
      console.log(data.value);
    } catch (error) {
      console.error(error);
    }
  };
  onMounted(async () => {
    await getData();
  });
</script>

<style scoped>
  /* For demo */
  :deep(.slick-slide) {
    text-align: center;
    height: 160px;
    line-height: 160px;
    background: #364d79;
    overflow: hidden;
  }

  :deep(.slick-arrow.custom-slick-arrow) {
    width: 25px;
    height: 25px;
    font-size: 25px;
    color: #fff;
    background-color: rgba(31, 45, 61, 0.11);
    transition: ease all 0.3s;
    opacity: 0.3;
    z-index: 1;
  }
  :deep(.slick-arrow.custom-slick-arrow:before) {
    display: none;
  }
  :deep(.slick-arrow.custom-slick-arrow:hover) {
    color: #fff;
    opacity: 0.5;
  }

  :deep(.slick-slide h3) {
    color: #fff;
  }
</style>
