import { defineStore } from 'pinia';
import axios from 'axios';
import { message } from 'ant-design-vue';

export const useExerciseStore = defineStore('exercise', {
  state: () => ({
    isZuotiMode: false, // 对应 sy.vue 中的 zuoti
    isSubmitting: false, // 标记是否正在提交答案
  }),
  actions: {
    toggleZuotiMode() {
      this.isZuotiMode = !this.isZuotiMode;
    },

    /**
     * 提交做题模式下的答案
     * @param {string} globalId - 题目的全局ID
     * @param {string} choice - 用户选择的答案 (A, B, C, D)
     * @param {string} type - 题目类型 (如 'sy' 或 'syzc')
     * @param {string} kjid - 关联的知识点ID
     * @param {Function} onSuccess - 提交成功后的回调函数
     * @param {Function} onError - 提交失败后的回调函数
     */
    async submitAnswer(globalId, choice, type, kjid, onSuccess, onError) {
      if (!this.isZuotiMode) {
        // 如果不是做题模式，不执行提交
        console.log('非做题模式，不提交答案。');
        return Promise.resolve(false);
      }

      // 如果正在提交，返回 false 表示提交失败
      if (this.isSubmitting) {
        console.log('正在提交中，请等待...');
        if (onError) onError('正在提交中，请等待...');
        return Promise.resolve(false);
      }

      this.isSubmitting = true; // 设置提交状态

      const urlx = '/egg/fbincr';
      let mode = 'syzc';
      if (type === 'sy' || type === 'syzc') {
        mode = 'syzc';
      }

      try {
        const response = await axios.post(urlx, {
          globalId: globalId,
          choice: choice,
          mode: mode,
          kjid: kjid,
        });

        console.log('做题模式提交结果:', response.data);
        if (response.data.data.msg) {
          message.success({
            content: `做题模式选择${choice}成功`,
            style: {
              marginTop: '60vh',
            },
          });
          if (onSuccess) onSuccess();
          return Promise.resolve(true); // 提交成功
        } else {
          // 即使没有msg，也可能需要通知外部，取决于具体业务逻辑
          if (onError) onError('提交失败: ' + (response.data.data.message || '未知错误'));
          return Promise.resolve(false); // 提交失败
        }
      } catch (error) {
        console.error('提交做题模式答案失败:', error);
        message.error({
          content: `做题模式选择${choice}失败`,
          style: {
            marginTop: '60vh',
          },
        });
        if (onError) onError(error);
        return Promise.resolve(false); // 提交失败
      } finally {
        this.isSubmitting = false; // 重置提交状态
      }
    },
  },
});
