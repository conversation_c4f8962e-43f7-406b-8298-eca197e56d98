<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <button type="button" class="top_button" :style="buttonStyles" @click="scrollToTop">
          ⬆️ 返回顶部
        </button>
        <div v-show="showTop" class="top">
          <a-space wrap>
            <a-button @click="toggleTop">隐藏顶部内容</a-button>
            <a-button type="primary" @click="ss('g')">搜索g</a-button>
            <a-button type="primary" @click="ss('s')">搜索s</a-button>
            <a-button type="primary" @click="uppic('g')">上传图片g</a-button>
            <a-button type="primary" @click="uppic('s')">上传图片s</a-button>
            <a-button @click="copyText">复制</a-button>
            <a-button @click="copyText(2)">复制2</a-button>
            <a-button class="showans" @click="togglean()">显示答案</a-button>
            <a-button @click="insertdata()">插入</a-button>
            <a-button @click="dellastdata()">删除最后一条</a-button>
            <!--            <a-button @click="toggleInterval">{{-->
            <!--              interstatus ? '关闭定时器' : '开启定时器'-->
            <!--            }}</a-button>-->
            <a href="/fb/nav3" target="_blank"><a-button @click="insertdata()">nav3</a-button></a>
            <a-button @click="interstatus1 = !interstatus1">{{
              interstatus1 ? '关闭自动' : '开启自动'
            }}</a-button>

            <a-select
              ref="select"
              v-model:value="value1"
              style="width: 120px"
              @focus="focus"
              @change="handleChange()"
            >
              <a-select-option value="48644">gwy</a-select-option>
              <a-select-option value="656604">sy</a-select-option>
              <a-select-option value="783922">gkgwy</a-select-option>
              <a-select-option value="786412">js</a-select-option>
            </a-select>
            <a-input v-model:value="qs" @change="getData()"></a-input>
            <a-input v-model:value="mo" @change="countnum = 0"></a-input>
            <a-input v-model:value="biaoming" placeholder="表名"></a-input>
            <a-button @click="countnum = 0">{{ countnum }}</a-button>
          </a-space>
        </div>
        <div style="margin-top: 10px">
          <div v-html="zltimu.content"></div>
          <div v-for="(item, index) in data" :key="index">
            <div v-if="isLoading">Loading...</div>
            <!--            <fbtag-->
            <!--              :messageFromParent="item.id"-->
            <!--              :messageFromParent1="value1 === 48644 ? 'gwy' : 'sy'"-->
            <!--            ></fbtag>-->
            <div v-if="!isLoading" class="contentx">
              <div v-html="item.content"></div>

              <div class="item" @click="togglean()">
                <p class="an_a" v-html="item.answerone"></p>
                <p class="an_b" v-html="item.answertwo"></p>
                <p class="an_c" v-html="item.answerthree"></p>
                <p class="an_d" v-html="item.answerfour"></p>
              </div>
              <p @click="toggleTop">====================================</p>
              <div v-show="showContent">
                <!--              <div v-html="item.answer"></div>-->
                <div style="color: mediumpurple" @click="open_comment(item.id)">
                  {{ item.source }}{{ item.createdTime
                  }}{{ '正确率：' + Math.round(item.correctRatio)
                  }}{{
                    '易错项：' +
                    (() => {
                      const val = +item.mostWrongAnswer;
                      return val === 0 ? 'A' : val === 1 ? 'B' : val === 2 ? 'C' : 'D';
                    })()
                  }}
                </div>
                <Shuxue :content="item.ds" />
                <br />
                <div class="answer" v-html="item.solution"></div>
                <p>====================================</p>
                <comment :uid="item.id" :type="value1"></comment>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import comment from '../../components/comment.vue';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';
  import { message } from 'ant-design-vue';
  import dp1 from '../../components/dp1.vue';
  import Shuxue from '@/components/shuxue.vue';
  const zltimu = ref([]);
  const data = ref([]);
  const isLoading = ref(true);
  const showTop = ref(true);
  const current1 = ref(1);
  const showContent = ref(true);
  const pagetotal = ref(20);
  const pageSize3 = ref(1);
  const total = ref(0);
  const isdangerarr = ref([false, false, false, false]);
  const route = useRoute();
  const fontStyle = ref(16);
  const value1 = ref(48644);
  const timu = ref('');
  const biaoming = ref('hsydsl');
  const qs = ref('');
  const timuid = ref(0);
  const mo = ref(1);
  const countnum = ref(0);
  const interstatus1 = ref(false);
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const scrollToTop = async () => {
    window.scrollTo({ top: 0, behavior: 'auto' });
  };
  const buttonStyles = ref({
    position: 'fixed',
    bottom: '20px',
    left: '50%',
    transform: 'translateX(-50%)',
    zIndex: '1000',
    padding: '10px',
    backgroundColor: '#000',
    color: '#fff',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    boxShadow: '0px 4px 6px rgba(0,0,0,0.1)',
  });
  const open_comment = (id) => {
    window.open(
      '/fb/comment?id=' + id + `&type=` + value1.value,
      '_blank',
      'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=800,height=600',
    );
  };
  const dellastdata = async () => {
    let params = {
      biao: biaoming.value,
    };
    let res = await axios.get(`/egg/dellastdata`, { params });
    console.log(res.data);
    if (res.data.code === 0) {
      message.success({
        content: res.data.message,
        duration: 1,
        style: {
          marginTop: '80vh',
        },
      });
      countnum.value = +countnum.value - 1;
    } else if (res.data.code === 1) {
      message.error({
        content: res.data.message,
        class: 'custom-class',
        duration: 1,
        style: {
          marginTop: '60vh',
          color: 'red',
        },
      });
    }
  };
  const insertdata = async () => {
    const url = '/egg/fbupdatezlfx5000';
    let params = {
      id: timuid.value,
      biao: biaoming.value,
    };
    // console.log('params', params);
    // console.log('data', data.value[0]);

    console.log(params);
    if (biaoming.value === '') {
      message.error({
        content: '表名不能为空',
        duration: 1,
        style: {
          marginTop: '60vh',
        },
      });
      return false;
    }

    try {
      // const response = await axios.get(url, { params });
      data.value[0].mo = +mo.value;
      data.value[0].type = +value1.value === 48644 ? 'xingce' : 'syzc';
      console.log(data.value[0]);
      const response = await axios.post('/egg/pushtimu', data.value[0], {
        params,
      });
      console.log(response);
      if (response.status === 200) {
        if (response.data.code === 0) {
          countnum.value = +countnum.value + 1;
          message.success({
            content: response.data.message,
            duration: 3,
            style: {
              // marginTop: '80vh',
            },
          });
        } else if (response.data.code === 1) {
          message.error({
            content: response.data.message,
            duration: 3,
            style: {
              // marginTop: '60vh',
              color: 'red',
            },
          });
        }
      }
    } catch (error) {
      message.error(error.response.data.message);
    }
  };
  const updateFontSize = () => {
    const wpElement = document.querySelector('.wp');
    wpElement.style.fontSize = `${fontStyle.value}px`;
  };
  const handleKeyDown = (event) => {
    if (event.key === 'ArrowLeft' || event.key === 'q') {
      getData();
    } else if (event.key === 'ArrowRight' || event.key === 'e') {
      insertdata();
    }
  };
  const right = () => {
    if (current1.value < 10000) {
      current1.value += 1;
      getData();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };

  const left = () => {
    if (current1.value > 1) {
      current1.value -= 1;
      getData();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };
  const toggleContent = async (event) => {
    if (
      event.key === ' ' ||
      event.key === 'Spacebar' ||
      event.key === 'w' ||
      event.key === 'ArrowUp' ||
      event.key === 'ArrowDown'
    ) {
      if (showContent.value) {
        await ansblack();
        isDanger(false);
        showContent.value = false;
      } else {
        await nextTick();
        await ansblack();
        await ansred();
        // const showAnsButton = document.querySelector('.showans');
        // showAnsButton.click();
        showContent.value = true;
      }
    }
  };

  const ansred = async () => {
    await nextTick();
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'red';
      }
    }
  };

  const ansblack = async () => {
    await nextTick();
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'black';
      }
    }
  };

  const togglean = async (answer) => {
    if (showContent.value) {
      await ansblack();
      isDanger(false);
      showContent.value = false;
    } else {
      await nextTick();
      await ansred();
      isDanger(answer);
      showContent.value = true;
    }
  };

  const pushans = async (answer) => {
    // const url = '/egg/minchoice';
    // data.value.choice = answer;
    // const response = await axios.post(url, data.value);
    // console.log(response);
  };
  const copyText = async (type = 1) => {
    let datax = data.value[0];
    console.log(datax);

    // 将<p>和</p>替换为换行符\n，并删除所有其他的HTML标签
    let contentWithoutHtml = datax.content.replace(/fenbike/g, 'fbstatic');
    // .replace(/<p>/gi, '') // 删除开头的<p>标签
    // .replace(/<\/p>/gi, '\n') // 将结束的</p>标签替换为换行符
    // .replace(/<[^>]*>/g, ''); // 删除所有其他HTML标签
    let solutionWithoutHtml = datax.solution.replace(/fenbike/g, 'fbstatic');
    // .replace(/<p>/gi, '') // 删除开头的<p>标签
    // .replace(/<\/p>/gi, '\n') // 将结束的</p>标签替换为换行符
    // .replace(/<[^>]*>/g, ''); // 删除所有其他HTML标签
    let solution = '';
    solution = +type === 2 ? solutionWithoutHtml : '';
    // 构建最终的文本
    let text = `
  ${contentWithoutHtml}\n
  ${datax.answerone}\n
  ${datax.answertwo}\n
  ${datax.answerthree}\n
  ${datax.answerfour}\n
  ====================================
  \n${solution}\n
  \n用丰富emoji加超级无敌说人话解释这题从什么信息入手可以在考场上10秒左右快速秒杀，先给我草稿纸极简秒题手稿（说人话极速理解版本，最关键的地方加粗），再说考场做题实操/实录，最后说题目的秒杀思路
  \n秒完撕卷下一题\n`;

    // 将文本复制到剪贴板
    await navigator.clipboard.writeText(text);
  };

  const isDanger = (answer) => {
    if (data.value && data.value.answer === answer && answer !== false) {
      switch (answer) {
        case 'A':
          isdangerarr.value[0] = true;
          break;
        case 'B':
          isdangerarr.value[1] = true;
          break;
        case 'C':
          isdangerarr.value[2] = true;
          break;
        case 'D':
          isdangerarr.value[3] = true;
          break;
      }
      return true;
    } else if (data.value && answer === false) {
      isdangerarr.value.fill(false);
    }
    return false;
  };
  const focus = () => {
    console.log('focus');
  };
  const handleChange = async (value) => {
    console.log(`selected ${value}`);
    await getData();
  };
  const ss = async (coursePrefix = 'g') => {
    qs.value = await navigator.clipboard.readText();
    value1.value = coursePrefix === 'g' ? 48644 : 656604;
    await getData();
    if (interstatus1.value === true) {
      await insertdata();
    }
    await nextTick();
    await ansred();
  };

  const uppic = async (coursePrefix = 'g') => {
    console.log('ClipboardPic0');
    try {
      // 使用 Clipboard API 获取剪贴板内容
      const clipboardItems = await navigator.clipboard.read();
      console.log('ClipboardPic1');
      for (const item of clipboardItems) {
        // 只处理图片类型
        const imageType = item.types.find((type) => type.startsWith('image/'));
        console.log('ClipboardPic2');
        await ansblack();
        if (imageType) {
          // 获取图片的 Blob 数据
          const blob = await item.getType(imageType);

          // 创建表单数据并附加图片
          const formData = new FormData();
          formData.append('file', blob, 'clipboard-image.png');
          formData.append('coursePrefix', coursePrefix === 'g' ? 48644 : 656604);
          console.log(formData);
          value1.value = coursePrefix === 'g' ? 48644 : 656604;
          // 上传图片
          const response = await axios.post('/egg/uppic', formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });

          // 打印响应结果
          console.log('上传成功:', response.data);
          data.value = response.data;
          console.log(data.value);
          for (let item in data.value) {
            if (!data.value[item].solution.match(/<p>A/g)) {
              data.value[item].solution = data.value[item].solution.replace(/A项/g, '<br/>A项');
            }
            if (!data.value[item].solution.match(/<p>B/g)) {
              data.value[item].solution = data.value[item].solution.replace(/B项/g, '<br/>B项');
            }
            if (!data.value[item].solution.match(/<p>C/g)) {
              data.value[item].solution = data.value[item].solution.replace(/C项/g, '<br/>C项');
            }
            if (!data.value[item].solution.match(/<p>D/g)) {
              data.value[item].solution = data.value[item].solution.replace(/D项/g, '<br/>D项');
            }
          }
          // total.value = response.data.pagetotal[0].total || 0;
          timuid.value = data.value.id;
          showContent.value = false;
          isLoading.value = false;
          showContent.value = !showContent.value;
          if (interstatus1.value === true) {
            await insertdata();
          }
        }
      }
      await nextTick();
      await ansred();
      await scrollToTop();
      await copyText(2);
    } catch (error) {
      console.error('上传图片失败:', error);
    }
  };
  const getData = async () => {
    const a = route.query.a || false;
    const z = route.query.z || 0;
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/chatimu';
    let clipboardData = await navigator.clipboard.readText();
    console.log('剪贴板', clipboardData);
    console.log('qs', qs.value);
    timu.value = clipboardData;
    if (qs.value) {
      timu.value = qs.value;
      clipboardData = qs.value;
    }
    let params = {
      type: value1.value,
      q: clipboardData,
    };
    console.log(params);
    try {
      await ansblack();
      const response = await axios.get(url, { params });

      data.value = response.data;
      console.log(data.value);
      for (let item in data.value) {
        data.value[item].content = data.value[item].content.replace(/fenbike/g, 'fbstatic');
        if (!data.value[item].solution.match(/<p>A/g)) {
          data.value[item].solution = data.value[item].solution.replace(/A项/g, '<br/>A项');
        }
        if (!data.value[item].solution.match(/<p>B/g)) {
          data.value[item].solution = data.value[item].solution.replace(/B项/g, '<br/>B项');
        }
        if (!data.value[item].solution.match(/<p>C/g)) {
          data.value[item].solution = data.value[item].solution.replace(/C项/g, '<br/>C项');
        }
        if (!data.value[item].solution.match(/<p>D/g)) {
          data.value[item].solution = data.value[item].solution.replace(/D项/g, '<br/>D项');
        }
      }
      // total.value = response.data.pagetotal[0].total || 0;
      timuid.value = data.value.id;
      showContent.value = false;
      isLoading.value = false;
      showContent.value = !showContent.value;
      // await togglean();
      await nextTick();
      window.document.title = `cha` + data.value[0].id;
      await ansred();
      await copyText(2);
      await scrollToTop();
      // if (showContent.value) {
      //   ansblack();
      //   showContent.value = false;
      // } else {
      //   ansred();
      //   showContent.value = true;
      // }
      // await insertdata();
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  const intervalId = ref(null);
  const tmpfz = ref('');
  const interstatus = ref(false);
  const jiaoyan = async () => {
    let clipboardData = await navigator.clipboard.readText();
    console.log('1', tmpfz.value, clipboardData);
    if (tmpfz.value !== clipboardData) {
      await ss('g');
      // await getData();
      await insertdata();
      tmpfz.value = clipboardData;
    }
    console.log('2', tmpfz.value, clipboardData);
  };
  const createInterval = () => {
    interstatus.value = true;
    intervalId.value = setInterval(jiaoyan, 2000); // 传递函数引用
  };

  const destroyInterval = () => {
    clearInterval(intervalId.value); // 传递间隔ID
    interstatus.value = false;
  };

  const toggleInterval = () => {
    if (interstatus.value) {
      console.log('1');
      destroyInterval();
    } else {
      console.log('2');
      createInterval();
    }
  };

  onMounted(() => {
    document.body.style.background = '#FAF5EB';
    document.addEventListener('keydown', toggleContent);
    // getData();
    window.addEventListener('keydown', handleKeyDown);
    updateFontSize();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
  });
</script>

<style scoped>
  .wp {
    position: relative;
    color: black;
    height: 100%;
    max-width: 960px;
  }
  .custom-class {
    color: red;
  }
  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }
  .top {
    position: sticky;
    top: 0;
    z-index: 10;
  }
  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }

  .taginput {
    user-select: none;
    cursor: pointer;
  }
</style>
