/* assets/main.css - 全局样式 */

/* CSS 变量定义 */
:root {
  --theme-bg: #f8f5e6;
  --theme-color: #333333;
  --theme-accent: #1677ee;
  --theme-secondary: #b39ddb;
  --table-bg: #fffefb;
  --table-header-bg: #f3eecb;
  --table-row-alt-bg: #f6f3e7;
  --table-border-color: #8b8680;
  --blockquote-bg: #e9e8d9;
  --theme-link-color: #2563eb;
  --theme-error-bg: #fef2f2;
  --theme-error-text: #dc2626;
  --theme-error-border: #fecaca;
  --image-bg-color: #fffefb;
}

/* 基础重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

/* 基础布局样式 */
html,
body,
#app {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  height: 100vh !important;
  min-height: 100vh !important;
  max-height: 100vh !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0 !important;
  padding: 0 !important;
  background: var(--theme-bg) !important;
  color: var(--theme-color) !important;
  box-sizing: border-box !important;
}

body {
  font-family: 'LXGW WenKai', serif;
}

/* 主题类样式 - 确保在生产环境中正确应用 */
body.theme-plain {
  background: #ffffff !important;
  color: #000000 !important;
}

body.theme-day {
  background: #f8f5e6 !important;
  color: #333333 !important;
}

body.theme-night {
  background: #222222 !important;
  color: #e0e0e0 !important;
}

/* 防止内容撑破 */
pre,
code,
img,
.maintimu,
.ds,
.answer {
  max-width: 100% !important;
  box-sizing: border-box;
  overflow-x: auto;
}

/* flex子项防止撑破 */
.datalist,
.top-input,
.btn-group {
  min-width: 0 !important;
}

.ant-row {
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.ant-col {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* 基础文本样式 */
.maintimu {
  font-family: 'LXGW WenKai', serif;
  font-size: 26px !important;
  color: var(--theme-color) !important;
}

.ds {
  font-family: 'LXGW WenKai', serif;
  font-size: 26px !important;
  color: var(--theme-color) !important;
}

.answer {
  color: var(--theme-secondary);
}

button {
  line-height: 0 !important;
}

img {
  max-width: 100%;
  max-height: 100%;
  display: inline;
}

/* 基础响应式设置 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media screen and (max-width: 600px) {
  .maintimu {
    font-size: 18px !important;
  }

  .ds {
    font-size: 16px !important;
  }
}

@media only screen and (max-width: 1024px) {
  * {
    margin: 0;
    padding: 2px;
  }
}

/* 打印样式 */
@media print {
  body {
    overflow: visible !important;
  }

  html,
  body {
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: visible !important;
  }

  * {
    box-sizing: border-box;
  }

  /* 防止内容超宽 */
  img,
  table,
  div,
  section {
    max-width: 100% !important;
    overflow: hidden !important;
  }

  /* 可以隐藏滚动条的容器 */
  ::-webkit-scrollbar {
    display: none;
  }
}
