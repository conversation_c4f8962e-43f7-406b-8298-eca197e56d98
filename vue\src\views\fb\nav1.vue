<template>
  <a-row>
    <a-col span="4">
      <p>
        <a href="/fb/nav1?type=gk"><el-button type="primary" plain>国考</el-button></a>
      </p>
      <p>
        <a href="/fb/nav1?type=gwy"><el-button type="primary" plain>公务员</el-button></a>
      </p>
      <p>
        <a href="/fb/nav1?type=zlfx"><el-button type="primary" plain>资料分析</el-button></a>
      </p>
      <p>
        <a href="/fb/nav2?type=gwy"><el-button type="primary" plain>nav2</el-button></a>
      </p>
    </a-col>
    <a-col span="16">
      <div class="button-container">
        <a-space>
          <template v-if="!isLoading">
            <div v-for="(subItems, item) in menuItems" :key="item.id" class="text item">
              <a
                :href="`/fb/sy?id=${subItems.id}&type=${type}&z=${
                  subItems.id === 48644 || subItems.id === 656604 ? '1' : '0'
                }`"
                target="_blank"
              >
                <el-button color="#626aef">{{ subItems.name }}{{ subItems.total }} </el-button>
              </a>
              <br />
              <br />
              <template v-if="subItems.children">
                {{ console.log(subItems.children) }}
                <div v-for="(childItem, index) in subItems.children" :key="index" class="text item">
                  <a
                    :href="`/fb/sy?id=${childItem.id}&type=${type}&z=${
                      subItems.id === 48644 || subItems.id === 656604 ? '1' : '0'
                    }`"
                    target="_blank"
                  >
                    <el-button type="warning">{{ childItem.name }}{{ childItem.total }} </el-button>
                  </a>
                  <br />
                  <br />
                  <template v-if="childItem.children">
                    <div v-for="(grandChildItem, gcIndex) in childItem.children" :key="gcIndex">
                      <a
                        :href="`/fb/sy?id=${grandChildItem.id}&type=${type}&z=${
                          subItems.id === 48644 || subItems.id === 656604 ? '1' : '0'
                        }`"
                        target="_blank"
                      >
                        <el-button type="primary"
                          >{{ grandChildItem.name }}{{ grandChildItem.total }}
                        </el-button>
                      </a>
                      <br />
                      <br />
                      <template v-if="grandChildItem.children">
                        <div
                          v-for="(grandChildItem, gcIndex) in grandChildItem.children"
                          :key="gcIndex"
                        >
                          <a
                            :href="`/fb/sy?id=${grandChildItem.id}&type=${type}&z=${
                              subItems.id === 48644 || subItems.id === 656604 ? '1' : '0'
                            }`"
                            target="_blank"
                          >
                            <el-button type="success"
                              >{{ grandChildItem.name }}{{ grandChildItem.total }}
                            </el-button>
                          </a>
                          <br />
                          <br />
                        </div>
                      </template>
                    </div>
                  </template>
                </div>
              </template>
            </div>
          </template>
          <p v-else><a-spin size="large" /></p>
        </a-space>
      </div>
    </a-col>
    <a-col span="4"></a-col>
  </a-row>
</template>

<script setup>
// 🔧 移除：不再需要手动管理主题
// import { useThemeStore } from '@/store/theme';
import axios from 'axios';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const menuItems = ref([]);
const isLoading = ref(true);
const type = ref('gwy');

const getData = async () => {
  const url = '/egg/fbtree1';
  const typex = route.query.type || type.value;
  const params = { type: typex };
  type.value = route.query.type || type.value;
  try {
    const response = await axios.get(url, { params });
    console.log(response.data);
    menuItems.value = response.data;
    isLoading.value = false;
  } catch (error) {
    console.error(error);
  }
};

// 🔧 移除：不再需要主题 store
// const themeStore = useThemeStore();

onMounted(async () => {
  // 🔧 简化：App.vue 会自动处理主题
  await getData();
});
</script>

<style scoped>
.button-container {
  text-align: center;
  margin-top: 20px;
}

a {
  text-decoration: none;
  margin-top: 20px;
}
</style>
