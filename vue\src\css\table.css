/* table.css - 表格样式系统 */

/* 表格容器样式 */
.table-container {
  max-width: 100% !important;
  box-sizing: border-box;
  overflow-x: auto;
  /* 隐藏滚动条 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.table-container::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* 自适应模式的表格限制宽度 */
.table-container.wrap-mode table {
  max-width: 100% !important;
  box-sizing: border-box;
}

/* 滚动模式的表格不限制宽度 */
.table-container.scroll-mode table {
  max-width: none !important;
  box-sizing: border-box;
}

/* 为表格滚动模式提供更好的显示效果 */
.scroll-container .table-container.scroll-mode {
  margin: 0; /* 不扩展，保持在容器内 */
  width: auto; /* 改为自动宽度，避免右侧空白 */
  max-width: 100%; /* 确保不超出容器 */
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  /* 确保滚动容器内的表格可以正常滚动 */
  overflow-x: auto;
  overflow-y: visible;
  /* 确保表格左对齐 */
  display: block;
}

/* 确保表格在滚动模式下有足够的空间但不超出容器 */
.scroll-container .table-container.scroll-mode table {
  min-width: max-content;
  width: auto;
  table-layout: auto;
}

/* 当表格处于滚动模式时，调整容器样式 */
.scroll-container:has(.table-container.scroll-mode) {
  overflow-x: visible !important;
  overflow-y: auto;
}

/* 表格在sy.vue中的特殊处理 */
table {
  max-width: 100% !important;
  box-sizing: border-box;
}

/* 滚动模式下的表格优化 - 避免不必要的空白 */
.table-container.scroll-mode table {
  max-width: none !important;
  width: auto !important; /* 自动宽度，根据内容调整 */
  min-width: max-content !important; /* 最小宽度为内容所需 */
}

/* ========== 专用表格样式系统 ========== */
/* 最高优先级样式，确保完全覆盖其他组件的表格样式 */

/* 基础表格样式重置 */
.markdown-content table {
  width: auto; /* 改为auto，让表格根据内容自适应宽度 */
  max-width: 100%; /* 但不超过容器宽度 */
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: auto;
  background: var(--table-bg, #fffefb);
  color: var(--theme-color, #333333);
  border: 1px solid var(--table-border-color, #b7b29e);
  border-radius: 6px;
  overflow: visible;
  box-sizing: border-box;
}

/* 表格单元格基础样式 */
.markdown-content th,
.markdown-content td {
  border: 1px solid var(--table-border-color, #b7b29e);
  text-align: left;
  box-sizing: border-box;
  padding: 12px 16px;
  vertical-align: top;
  font-family: 'LXGW WenKai', serif;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 表头样式 */
.markdown-content th {
  background: var(--table-header-bg, #f3eecb);
  color: var(--theme-color, #333333);
  font-weight: 600;
  border-bottom: 2px solid var(--table-border-color, #b7b29e);
}

/* 表格行样式 */
.markdown-content td {
  background: var(--table-bg, #fffefb);
  color: var(--theme-color, #333333);
}

.markdown-content tr:nth-child(even) td {
  background-color: var(--table-row-alt-bg, #f6f3e7);
}

/* 移除最后一列的右边框 */
.markdown-content th:last-child,
.markdown-content td:last-child {
  border-right: none;
}

/* ========== 表格容器样式系统 ========== */

/* 基础表格容器样式 */
.markdown-content .table-container {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  margin: 1.2rem 0;
  border-radius: 6px;
  background: var(--table-row-alt-bg, #f6f3e7);
  padding: 0; /* 修复：移除内边距，防止表格内容被隐藏 */
  box-sizing: border-box;
  /* 默认隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
  /* 修复：改为左对齐，不居中，确保表格左侧内容可见 */
  display: block;
}

.markdown-content .table-container::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.markdown-content .table-container::-webkit-scrollbar-track {
  display: none;
}

.markdown-content .table-container::-webkit-scrollbar-thumb {
  display: none;
}

/* ========== 自适应模式样式 ========== */
.markdown-content .table-container.wrap-mode {
  overflow-x: visible;
  overflow-y: visible;
  width: 100%;
  /* 修复：自适应模式下也改为左对齐 */
  display: block;
}

.markdown-content .table-container.wrap-mode table {
  width: auto; /* 自适应模式下让表格根据内容确定宽度 */
  table-layout: auto;
  /* 让表格根据内容自适应宽度 */
  max-width: 100%;
}

.markdown-content .table-container.wrap-mode td,
.markdown-content .table-container.wrap-mode th {
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  overflow-wrap: break-word;
  padding: 8px 12px;
  vertical-align: top;
  /* 让单元格根据内容自适应宽度 */
  width: auto;
  min-width: auto;
  max-width: none;
}

/* 移除限制性的列宽设置，让表格自然适应内容 */

/* ========== 滚动模式样式 ========== */
.markdown-content .table-container.scroll-mode {
  overflow-x: auto;
  overflow-y: visible;
  width: auto; /* 改为自动宽度，避免右侧空白 */
  max-width: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 0; /* 确保滚动模式下也没有内边距 */
  /* 显示滚动条以便用户知道可以滚动 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
  position: relative;
}

/* 自定义滚动条样式 - 更好的用户体验 */
.markdown-content .table-container.scroll-mode::-webkit-scrollbar {
  height: 8px;
  background: #f1f5f9;
  border-radius: 4px;
}

.markdown-content .table-container.scroll-mode::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.markdown-content .table-container.scroll-mode::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.markdown-content .table-container.scroll-mode::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.markdown-content .table-container.scroll-mode table {
  min-width: max-content;
  width: auto;
  margin: 0;
  table-layout: auto;
}

.markdown-content .table-container.scroll-mode th,
.markdown-content .table-container.scroll-mode td {
  white-space: nowrap;
  padding: 12px 16px;
  min-width: auto; /* 让滚动模式下的单元格也能自适应内容 */
  max-width: none;
}

/* 覆盖可能存在的第3列固定宽度设置 */
.table-container.scroll-mode th:nth-child(3),
.table-container.scroll-mode td:nth-child(3) {
  width: auto !important;
  min-width: auto !important;
  max-width: none !important;
}

/* 同样覆盖自适应模式下的第3列宽度 */
.table-container.wrap-mode th:nth-child(3),
.table-container.wrap-mode td:nth-child(3) {
  width: auto !important;
  min-width: auto !important;
  max-width: none !important;
}

/* 通用覆盖，确保所有表格列都能自适应 */
.markdown-content .table-container th,
.markdown-content .table-container td {
  width: auto !important;
  min-width: auto !important;
}
