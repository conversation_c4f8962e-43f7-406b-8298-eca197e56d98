<script>
  import { defineComponent } from 'vue';
  import { message } from 'ant-design-vue';

  export default defineComponent({
    name: 'Timu',
    data: function () {
      return {
        timu: '',
        ID: '',
        A: '',
        B: '',
        C: '',
        D: '',
        zhengque: '',
        jiexi: '',
        moduleName: '言语理解',
      };
    },
    created() {},
    mounted() {
      this.getId();
    },
    methods: {
      fenxi() {
        const regex = /[A-D]\.\s+(.+)/g;
        let matches;
        const options = [];

        while ((matches = regex.exec(this.timu)) !== null) {
          options.push(matches[1]);
        }
        const question = this.timu.replace(regex, '');

        const [A, B, C, D] = options;
        // console.log('Question:', question.trim());
        // console.log('A:', A);
        // console.log('B:', B);
        // console.log('C:', C);
        // console.log('D:', D);
        this.timu = question.trim();
        this.A = 'A.' + A;
        this.B = 'B.' + B;
        this.C = 'C.' + C;
        this.D = 'D.' + D;
      },
      fenxians() {
        const regex = /【答案】([A-Za-z])/;

        const match = this.jiexi.match(regex);
        if (match) {
          const answer = match[1];
          console.log('Answer:', answer);
          this.zhengque = answer;
        } else {
          console.log('Answer not found.');
        }
      },
      pushtimu(text) {
        try {
          window
            .axios('https://egg.wcy9.com/intimu', {
              method: 'post',
              data: {
                timu: this.timu,
                ID: this.ID,
                A: this.A,
                B: this.B,
                C: this.C,
                D: this.D,
                zhengque: this.zhengque,
                jiexi: this.jiexi,
                moduleName: this.moduleName,
              },
            })
            .then((res) => {
              message.success('成功添加' + res.data.affectedRows + '条');
              console.log(res.data);
              this.getId();
            })
            .catch((err) => {
              // message.error('This is an error message: ' + err.message);
              message.error('This is an error message: ' + err.response.data.data);
              console.log(err);
            });
        } catch (err) {
          message.error('This is an error message');
          // console.log(err);
        }
      },
      getId() {
        try {
          window.axios('https://egg.wcy9.com/intimu?id=1').then((res) => {
            console.log(res.data);
            this.ID = res.data.lastid + 1;
          });
        } catch (err) {}
      },
      reset() {
        this.timu = '';
        this.A = '';
        this.B = '';
        this.C = '';
        this.D = '';
        this.zhengque = '';
        this.jiexi = '';
      },
    },
  });
</script>

<template>
  <a-row>
    <a-col :span="5">col-4</a-col>
    <a-col :span="14">
      <a-textarea v-model:value="timu" placeholder="题目" :rows="8" @blur="fenxi()" />
      <br />
      <a-row style="margin-top: 20px">
        <a-col :span="1">ID:</a-col>
        <a-col :span="23">
          <a-input v-model:value="ID" placeholder="Basic usage" />
        </a-col>
      </a-row>
      <a-row style="margin-top: 20px">
        <a-col :span="2">模块:</a-col>
        <a-col :span="22">
          <a-input v-model:value="moduleName" placeholder="Basic usage" />
        </a-col>
      </a-row>
      <a-row style="margin-top: 20px">
        <a-col :span="1">A:</a-col>
        <a-col :span="23">
          <a-input v-model:value="A" placeholder="Basic usage" />
        </a-col>
      </a-row>
      <a-row style="margin-top: 20px">
        <a-col :span="1">B:</a-col>
        <a-col :span="23">
          <a-input v-model:value="B" placeholder="Basic usage" />
        </a-col>
      </a-row>
      <a-row style="margin-top: 20px">
        <a-col :span="1">C:</a-col>
        <a-col :span="23">
          <a-input v-model:value="C" placeholder="Basic usage" />
        </a-col>
      </a-row>
      <a-row style="margin-top: 20px">
        <a-col :span="1">D:</a-col>
        <a-col :span="23">
          <a-input v-model:value="D" placeholder="Basic usage" />
        </a-col>
      </a-row>
      <a-row style="margin-top: 20px">
        <a-col :span="2">正确答案：</a-col>
        <a-col :span="22">
          <a-input v-model:value="zhengque" placeholder="Basic usage" />
        </a-col>
      </a-row>

      <a-textarea
        v-model:value="jiexi"
        placeholder="解析"
        :rows="4"
        style="margin-top: 20px"
        @blur="fenxians()"
      />
      <a-button type="primary" style="margin-top: 20px" @click="pushtimu">Primary Button</a-button>
      <a-button type="primary" style="margin-top: 20px; margin-left: 20px" @click="reset()"
        >Reset</a-button
      >
    </a-col>
    <a-col :span="5">col-4</a-col>
  </a-row>
</template>

<style scoped></style>
