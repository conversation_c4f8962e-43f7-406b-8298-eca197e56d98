<template>
  <a-row>
    <a-col span="4"></a-col>
    <a-col span="16">
      <div class="button-container">
        <a-space>
          <template v-if="!isLoading">
            <div v-for="(subItems, item) in menuItems" :key="item.id" class="text item">
              <a
                :href="`/fb/sy?id=${subItems.id}&type=${type}&z=${
                  subItems.id === 656604 ? '1' : '0'
                }`"
                target="_blank"
              >
                <el-button color="#626aef">{{ subItems.name }}{{ subItems.total }} </el-button>
              </a>
              <br />
              <br />
              <template v-if="subItems.children">
                {{ console.log(subItems.children) }}
                <div v-for="(childItem, index) in subItems.children" :key="index" class="text item">
                  <a
                    :href="`/fb/sy?id=${childItem.id}&type=${type}&z=${
                      subItems.id === 656604 ? '1' : '0'
                    }`"
                    target="_blank"
                  >
                    <el-button type="warning">{{ childItem.name }}{{ childItem.total }} </el-button>
                  </a>
                  <br />
                  <br />
                  <template v-if="childItem.children">
                    <div v-for="(grandChildItem, gcIndex) in childItem.children" :key="gcIndex">
                      <a
                        :href="`/fb/sy?id=${grandChildItem.id}&type=${type}&z=${
                          subItems.id === 656604 ? '1' : '0'
                        }`"
                        target="_blank"
                      >
                        <el-button type="primary"
                          >{{ grandChildItem.name }}{{ grandChildItem.total }}
                        </el-button>
                      </a>
                      <br />
                      <br />
                      <template v-if="grandChildItem.children">
                        <div
                          v-for="(grandChildItem, gcIndex) in grandChildItem.children"
                          :key="gcIndex"
                        >
                          <a
                            :href="`/fb/sy?id=${grandChildItem.id}&type=${type}&z=${
                              subItems.id === 656604 ? '1' : '0'
                            }`"
                            target="_blank"
                          >
                            <el-button type="success"
                              >{{ grandChildItem.name }}{{ grandChildItem.total }}
                            </el-button>
                          </a>
                          <br />
                          <br />
                        </div>
                      </template>
                    </div>
                  </template>
                </div>
              </template>
            </div>
          </template>
          <p v-else>Loading...</p>
        </a-space>
      </div>
    </a-col>
    <a-col span="4"></a-col>
  </a-row>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const menuItems = ref([]);
  const isLoading = ref(true);
  const type = ref('sy');

  const getData = async () => {
    const url = '/egg/fbtree1';
    const params = { type: route.query.type || type.value };
    try {
      const response = await axios.get(url, { params });
      console.log(response.data);
      menuItems.value = response.data;
      isLoading.value = false;
    } catch (error) {
      console.error(error);
    }
  };

  onMounted(async () => {
    await getData();
  });
</script>

<style scoped>
  .button-container {
    text-align: center;
    margin-top: 20px;
  }

  a {
    text-decoration: none;
    margin-top: 20px;
  }
</style>
