<template>
  <div id="app">
    <div style="background: #3c464f; width: 1000px; height: 1000px"></div>

    <canvas
      id="drawingCanvas"
      style="position: absolute; top: 0; left: 0; background-color: rgba(255, 255, 255, 0)"
      @mousedown="startDrawing"
      @mouseup="stopDrawing"
      @mousemove="draw"
      @mouseleave="stopDrawing"
    ></canvas>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        ctx: null,
        drawing: false,
      };
    },
    mounted() {
      this.initCanvas();
    },
    methods: {
      initCanvas() {
        const canvas = document.getElementById('drawingCanvas');
        this.ctx = canvas.getContext('2d');
      },
      startDrawing(event) {
        this.drawing = true;
        this.draw(event);
      },
      stopDrawing() {
        this.drawing = false;
        this.ctx.beginPath();
      },
      draw(event) {
        if (!this.drawing) return;
        this.ctx.lineWidth = 1;
        this.ctx.lineCap = 'round';
        this.ctx.lineTo(event.offsetX, event.offsetY);
        this.ctx.stroke();
        this.ctx.beginPath();
        this.ctx.moveTo(event.offsetX, event.offsetY);
      },
    },
  };
</script>

<style>
  canvas {
    border: 1px solid black;
  }
</style>
