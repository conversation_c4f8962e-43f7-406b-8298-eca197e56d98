/**
 * 数学公式模板库
 * 为常见的逻辑推理题型提供标准化的数学表达式
 */

export const formulaTemplates = {
  // 定义判断类公式
  definition: {
    // 基础定义结构
    basic: (concept, elements) => `
$$
\\textcolor{red}{\\text{${concept}}} = ${elements.map((el, i) => 
  `\\underbrace{\\textcolor{blue}{\\text{${el.name}}}}_{\\text{${el.desc}}}`
).join(' + ')}
$$`,

    // 双要素结构（如您的平等理论）
    dualElements: (concept, element1, element2, result) => `
$$
\\textcolor{red}{\\text{${concept}}} = \\underbrace{\\textcolor{blue}{\\text{${element1.name}}}}_{\\text{${element1.desc}}} + \\underbrace{\\textcolor{orange}{\\text{${element2.name}}}}_{\\text{${element2.desc}}} \\rightarrow \\boxed{\\textcolor{red}{\\text{${result}}}}
$$`,

    // 条件判断结构
    conditional: (condition, cases) => `
$$
\\text{${condition}} \\begin{cases} 
${cases.map(c => `\\textcolor{${c.color || 'black'}}{\\text{${c.text}}}`).join(' \\\\\\\\ ')}
\\end{cases}
$$`
  },

  // 类比推理公式
  analogy: {
    // 交叉关系
    crossRelation: (term1, term2, symbol = '\\times') => `
$$
\\text{${term1.name}}(\\text{${term1.type}}) \\textcolor{red}{${symbol}} \\text{${term2.name}}(\\text{${term2.type}})
$$`,

    // 关系对比表
    relationTable: (relations) => `
| 选项 | ${relations.headers.join(' | ')} |
|------|${relations.headers.map(() => '----------').join('|')}|
${relations.rows.map(row => `| ${row.join(' | ')} |`).join('\n')}
`
  },

  // 逻辑推理公式
  logic: {
    // 充分必要条件
    suffNec: (condition, result, type = 'sufficient') => {
      const symbols = {
        sufficient: '\\Rightarrow',
        necessary: '\\Leftarrow', 
        both: '\\Leftrightarrow'
      };
      return `$$\\text{${condition}} ${symbols[type]} \\text{${result}}$$`;
    },

    // 集合关系
    setRelation: (setA, setB, relation = 'subset') => {
      const symbols = {
        subset: '\\subset',
        superset: '\\supset',
        equal: '=',
        intersect: '\\cap',
        union: '\\cup',
        disjoint: '\\cap = \\varnothing'
      };
      return `$$\\text{${setA}} ${symbols[relation]} \\text{${setB}}$$`;
    }
  },

  // 选项分析模板
  optionAnalysis: {
    // 要素检查表
    elementCheck: (options, elements) => `
| 选项 | ${elements.join(' | ')} | 结论 |
|------|${elements.map(() => '----------').join('|')}|----------|
${options.map(opt => `| ${opt.name} | ${opt.checks.join(' | ')} | **${opt.result}** |`).join('\n')}
`,

    // 错误类型分析
    errorAnalysis: (options) => `
$$
\\text{错误类型分析} \\begin{cases} 
${options.map(opt => `\\text{${opt.name}：}\\textcolor{red}{\\text{${opt.error}}}`).join(' \\\\\\\\ ')}
\\end{cases}
$$`
  }
};

// 预设的常用公式
export const commonFormulas = {
  // 您的平等理论公式（优化版）
  equalityTheory: `
$$
\\textcolor{red}{\\text{平等理论}} = \\underbrace{\\textcolor{blue}{\\text{领域隔离}}}_{\\text{砌墙}} + \\underbrace{\\textcolor{orange}{\\text{禁越界扩张}}}_{\\text{通电}} \\quad \\xrightarrow{\\textcolor{green}{\\text{B项}}} \\boxed{\\textcolor{red}{\\text{官商防火墙}}\\,🔥} \\\\[8pt]
\\textcolor{gray}{\\text{其他选项}} \\begin{cases} 
\\textcolor{red}{\\text{A：只发奖牌没建墙}} \\\\\\\\ 
\\textcolor{red}{\\text{C：鼓励跨栏抢劫}} \\\\\\\\ 
\\textcolor{red}{\\text{D：有山无哨卡}} 
\\end{cases}
$$`,

  // 交叉关系判定
  crossRelationCheck: `
$$
\\textcolor{green}{\\text{A} \\not\\subseteq \\text{B}} \\quad \\& \\quad \\textcolor{green}{\\text{B} \\not\\subseteq \\text{A}} \\quad \\& \\quad \\textcolor{green}{\\text{A} \\cap \\text{B} \\neq \\varnothing}
$$`,

  // 定义判断通用公式
  definitionJudgment: `
$$
\\textcolor{red}{\\text{定义判断}} \\triangleq \\underbrace{\\text{核心要素识别}}_{\\text{抓关键词}} + \\underbrace{\\text{选项逐一验证}}_{\\text{缺一不可}}
$$`
};

// 公式生成器
export class FormulaGenerator {
  // 生成双要素定义公式
  static generateDualDefinition(concept, element1, element2, result) {
    return formulaTemplates.definition.dualElements(concept, element1, element2, result);
  }

  // 生成选项分析表
  static generateOptionTable(options, criteria) {
    return formulaTemplates.optionAnalysis.elementCheck(options, criteria);
  }

  // 生成错误分析
  static generateErrorAnalysis(options) {
    return formulaTemplates.optionAnalysis.errorAnalysis(options);
  }

  // 优化现有公式
  static optimizeFormula(formula) {
    let optimized = formula;
    
    // 修复常见语法问题
    optimized = optimized.replace(/\\(\[\d+pt\])/g, '\\\\$1');
    optimized = optimized.replace(/(\\begin\{cases\}[\s\S]*?)\\(\s)/g, '$1\\\\$2');
    
    // 优化emoji显示
    optimized = optimized.replace(/(🔥|⚡|💥|🚧|🔒|🔑|⚖️|🛑|💸|⛰️|🏆|📊|💎)/g, '\\,\\text{$1}\\,');
    
    return optimized;
  }
}

// 使用示例
export const examples = {
  // 您的平等理论示例
  equalityTheoryExample: {
    concept: "平等理论",
    element1: { name: "领域隔离", desc: "砌墙" },
    element2: { name: "禁越界扩张", desc: "通电" },
    result: "官商防火墙🔥",
    options: [
      { name: "A", checks: ["❌", "❌"], result: "死" },
      { name: "B", checks: ["✅", "✅"], result: "活" },
      { name: "C", checks: ["❌", "❌"], result: "死" },
      { name: "D", checks: ["️", "❌"], result: "死" }
    ]
  }
};

export default formulaTemplates;
