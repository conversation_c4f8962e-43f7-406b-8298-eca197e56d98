<template>
  <div class="min-h-screen flex items-center justify-center">
    <el-space direction="vertical">
      <el-upload
        class="upload-demo"
        drag
        action="https://egg.101818.xyz/tele/upload"
        :on-success="handleSuccess"
        multiple
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">Drop file here or <em>click to upload</em></div>
        <template #tip>
          <div class="el-upload__tip">jpg/png files with a size less than 500kb</div>
        </template>
      </el-upload>

      <el-space direction="vertical">
        <div v-for="(o, index) in imglist" :key="o" class="text item" style="display: flex">
          <el-card :key="o" class="box-card" style="width: 960px">
            <div style="display: flex">
              <a-image :width="200" :src="`https://tgimg.101616.xyz${o}`" class="image" />
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  width: 100%;
                "
              >
                <div>
                  直接链接
                  <el-input
                    ref="input1"
                    placeholder="Please input"
                    :value="`https://tgimg.101616.xyz${o}`"
                    :class="'input1-' + index"
                  >
                    <template #append>
                      <el-button type="primary" plain @click="copyInput1(index)">Copy</el-button>
                    </template>
                  </el-input>
                </div>
                <div>
                  markdown
                  <el-input
                    ref="input2"
                    placeholder="Please input"
                    :value="`![image](${`https://tgimg.101616.xyz${o}`})`"
                    :class="'input2-' + index"
                  >
                    <template #append>
                      <el-button type="primary" plain @click="copyInput2(index)">Copy</el-button>
                    </template>
                  </el-input>
                </div>
                <div>
                  HTML
                  <el-input
                    ref="input3"
                    placeholder="Please input"
                    :value="`<img src='https://tgimg.101616.xyz${o}' width='20%'>`"
                    :class="'input3-' + index"
                  >
                    <template #append>
                      <el-button type="primary" plain @click="copyInput3(index)">Copy</el-button>
                    </template>
                  </el-input>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-space>
    </el-space>
  </div>
</template>
<script>
  import { defineComponent } from 'vue';
  import { UploadFilled } from '@element-plus/icons-vue';
  export default defineComponent({
    name: 'PluploadView',
    data: function () {
      return {
        imglist: [],
      };
    },
    created() {},
    mounted() {},
    methods: {
      handleSuccess(response, file, fileList) {
        this.imglist.push(response[0].src);
        console.log(this.imglist);
      },
      copyInput1(index) {
        const input = this.$refs[`input1`][index].$refs.input.value;
        this.copyToClipboard(input);
      },
      copyInput2(index) {
        const input = this.$refs[`input2`][index].$refs.input.value;
        this.copyToClipboard(input);
      },
      copyInput3(index) {
        const input = this.$refs[`input3`][index].$refs.input.value;
        this.copyToClipboard(input);
      },
      copyToClipboard(text) {
        try {
          navigator.clipboard.writeText(text);
          this.$message.success('复制成功!');
        } catch (err) {
          console.error('Failed to copy text: ', err);
          this.$message.error('复制失败!');
        }
      },
    },
  });
</script>

<style scoped></style>
