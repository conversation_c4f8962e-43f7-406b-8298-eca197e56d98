<template>
  <a-row>
    <a-col span="4"><Time></Time></a-col>
    <a-col span="16">
      <div class="button-container">
        <template v-if="!isLoading">
          <template v-for="(subItems, item) in menuItems" :key="item">
            <p>{{ item }}{{ refinishedItems[item] }}{{ refinish(item, 'm') }}</p>
            <a
              v-for="subItem in subItems"
              :key="subItem"
              :href="`/min?f=${subItem}`"
              target="_blank"
            >
              <a-button
                >{{ subItem }}{{ refinishedItems[subItem] }}{{ refinish(subItem, 'f') }}</a-button
              >
            </a>
          </template>
        </template>
        <p v-else>Loading...</p>
      </div>
    </a-col>
    <a-col span="4"></a-col>
  </a-row>
</template>

<script>
  import { onMounted, ref } from 'vue';
  import { Button } from 'ant-design-vue';
  import axios from 'axios';
  import Time from '../../components/time.vue';

  export default {
    name: 'Minnav',
    components: {
      'a-button': Button,
    },
    setup() {
      const menuItems = ref([]);
      const isLoading = ref(true);
      const refinishedItems = ref([]);

      const refinish = (text, m) => {
        if (refinishedItems.value[text] || text === '' || text === undefined || text === null) {
          return '';
        }

        const url = '/egg/minrefinish';
        const params = {
          text: text,
          m: m,
        };

        if (text === '') {
          return;
        }

        try {
          axios.get(url, { params }).then((res) => {
            console.log(text + '请求完成');

            refinishedItems.value[text] = res.data.st;
          });
        } catch (error) {
          console.error(error);
        }
      };

      const getData = async () => {
        const url = '/egg/minnav';
        const params = {};

        try {
          const response = await axios.get(url, { params });
          menuItems.value = response.data;
          isLoading.value = false;
          console.log(menuItems.value);
        } catch (error) {
          console.error(error);
        }
      };

      onMounted(() => {
        getData();
      });

      return {
        menuItems,
        isLoading,
        refinishedItems,
        refinish,
      };
    },
  };
</script>

<style scoped>
  .button-container {
    text-align: center;
    margin-top: 20px;
  }
</style>
