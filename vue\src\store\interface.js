// src/store/interface.js
import { defineStore } from 'pinia';

export const useInterfaceStore = defineStore('interface', {
  state: () => ({
    theme: 'day', // 'day' or 'night'
    isGridMode: window.innerWidth > 768,
    isCanvasVisible: false,
  }),
  actions: {
    setTheme(newTheme) {
      this.theme = newTheme;

      const themes = {
        day: {
          '--theme-bg': '#f8f5e6',
          '--theme-color': '#333333',
          '--theme-accent': '#1677ee',
          '--theme-secondary': '#b39ddb',
          '--table-bg': '#fffefb',
          '--table-header-bg': '#f3eecb',
          '--table-row-alt-bg': '#f6f3e7',
          '--table-border-color': '#b7b29e',
          '--blockquote-bg': '#e9e8d9',
          '--theme-link-color': '#2563eb',
          '--theme-error-bg': '#fef2f2',
          '--theme-error-text': '#dc2626',
          '--theme-error-border': '#fecaca',
          '--image-bg-color': '#fffefb',
        },
        night: {
          '--theme-bg': '#222222',
          '--theme-color': '#e0e0e0',
          '--theme-accent': '#4fc3f7',
          '--theme-secondary': '#9575cd',
          '--table-bg': '#23272e',
          '--table-header-bg': '#2d313a',
          '--table-row-alt-bg': '#262a32',
          '--table-border-color': '#424242',
          '--blockquote-bg': '#2c2c2c',
          '--theme-link-color': '#60a5fa',
          '--theme-error-bg': '#4a2222',
          '--theme-error-text': '#fda4af',
          '--theme-error-border': '#8f2d2d',
          '--image-bg-color': 'var(--theme-bg)',
        },
      };

      const selectedTheme = themes[this.theme];
      for (const [key, value] of Object.entries(selectedTheme)) {
        document.documentElement.style.setProperty(key, value);
      }
    },
    setGridMode(width) {
      this.isGridMode = width > 768;
    },
    toggleCanvas() {
      this.isCanvasVisible = !this.isCanvasVisible;
    },
  },
});
