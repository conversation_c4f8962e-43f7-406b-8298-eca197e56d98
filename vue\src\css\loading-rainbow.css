/* loading-rainbow.css - 加载动画样式 */

/* 加载动画容器 */
.fancy-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* 彩虹旋转器 */
.rainbow-spinner {
  position: relative;
  width: 70px;
  height: 70px;
  animation: spinner-rotate-counter 2s linear infinite;
}

.rainbow-spinner span {
  display: block;
  position: absolute;
  left: 33px;
  top: 5px;
  width: 5px;
  height: 22px;
  border-radius: 3px;
  opacity: 0.8;
  transform-origin: 50% 30px;
  /* 每条线只显示三分之一的圆弧 */
  clip-path: polygon(0% 0%, 100% 0%, 100% 33.33%, 0% 33.33%);
}

.rainbow1 {
  background: linear-gradient(180deg, #ff3c3c, #ffb347);
}

.rainbow2 {
  background: linear-gradient(180deg, #ffb347, #fff700);
}

.rainbow3 {
  background: linear-gradient(180deg, #fff700, #7fff00);
}

.rainbow4 {
  background: linear-gradient(180deg, #7fff00, #00eaff);
}

.rainbow5 {
  background: linear-gradient(180deg, #00eaff, #3c6cff);
}

.rainbow6 {
  background: linear-gradient(180deg, #3c6cff, #a259ff);
}

.rainbow7 {
  background: linear-gradient(180deg, #a259ff, #ff3c3c);
}

.rainbow8 {
  background: linear-gradient(180deg, #ff3c3c, #ffb347);
}

/* 8条线条，每条依次落后，逆时针旋转 */
.rainbow-spinner span:nth-child(1) {
  transform: rotate(0deg);
  animation: line-rotate-counter 2s linear infinite;
  animation-delay: 0s;
}
.rainbow-spinner span:nth-child(2) {
  transform: rotate(45deg);
  animation: line-rotate-counter 2s linear infinite;
  animation-delay: -0.25s;
}
.rainbow-spinner span:nth-child(3) {
  transform: rotate(90deg);
  animation: line-rotate-counter 2s linear infinite;
  animation-delay: -0.5s;
}
.rainbow-spinner span:nth-child(4) {
  transform: rotate(135deg);
  animation: line-rotate-counter 2s linear infinite;
  animation-delay: -0.75s;
}
.rainbow-spinner span:nth-child(5) {
  transform: rotate(180deg);
  animation: line-rotate-counter 2s linear infinite;
  animation-delay: -1s;
}
.rainbow-spinner span:nth-child(6) {
  transform: rotate(225deg);
  animation: line-rotate-counter 2s linear infinite;
  animation-delay: -1.25s;
}
.rainbow-spinner span:nth-child(7) {
  transform: rotate(270deg);
  animation: line-rotate-counter 2s linear infinite;
  animation-delay: -1.5s;
}
.rainbow-spinner span:nth-child(8) {
  transform: rotate(315deg);
  animation: line-rotate-counter 2s linear infinite;
  animation-delay: -1.75s;
}

/* 逆时针旋转动画 */
@keyframes spinner-rotate-counter {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

/* 每条线的独立逆时针旋转动画，只显示三分之一圆弧 */
@keyframes line-rotate-counter {
  0% {
    opacity: 1;
    transform: rotate(0deg) scaleY(1);
  }
  33.33% {
    opacity: 0.3;
    transform: rotate(-120deg) scaleY(0.7);
  }
  66.66% {
    opacity: 0.1;
    transform: rotate(-240deg) scaleY(0.5);
  }
  100% {
    opacity: 1;
    transform: rotate(-360deg) scaleY(1);
  }
}

.fancy-text {
  font-size: 20px;
  color: #6366f1;
  font-weight: bold;
  letter-spacing: 2px;
  margin-top: 8px;
  text-shadow: 0 2px 8px #e0e7ff;
}
