<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div v-show="showTop" class="top">
          <a-button @click="increaseFontSize">增大字体</a-button>
          <a-button @click="togglean(data.referenceAnswer)">显示答案</a-button>
          <a-button>{{ total }}</a-button>
          <a-button @click="toggleTop">隐藏顶部内容</a-button>
          <a-input v-model:value="biaoming" placeholder="表名"></a-input>
          <el-pagination
            v-model:current-page="current1"
            v-model:page-size="pageSize3"
            show-quick-jumper
            :page-sizes="[1, 4, 8, 20, 10, 50, 62, 100, 200, 300, 400, 2000]"
            layout="sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="getData"
            @current-change="getData"
          />
        </div>
        <div v-for="(item, index) in data" :key="index">
          <div v-if="isLoading">Loading...</div>
          <div v-if="!isLoading">
            <p @click="toggleTop">================</p>

            <div v-html="item.content.replace(/&nbsp;/g, '').replace(/<p>\s*/g, '<p>')"></div>
            <!-- <p @click="toggleTop" style="color: #c45656">================</p> -->
            <div v-for="(childItem, index) in item.children" :key="index">
              <div class="item">
                <div v-html="childItem.content"></div>
                <div v-if="childItem.xuanxiang" class="item">
                  <p v-html="childItem.xuanxiang"></p>
                </div>
                <!--                <a-row>-->
                <!--                  <a-col :span="24">-->
                <!--                    <p class="an_a" v-html="childItem.answerone"></p>-->
                <!--                  </a-col>-->
                <!--                  <a-col :span="24"-->
                <!--                    ><p class="an_b" v-html="childItem.answertwo"></p>-->
                <!--                  </a-col>-->
                <!--                  <a-col :span="24"-->
                <!--                    ><p class="an_c" v-html="childItem.answerthree"></p>-->
                <!--                  </a-col>-->
                <!--                  <a-col :span="24"-->
                <!--                    ><p class="an_d" v-html="childItem.answerfour"></p>-->
                <!--                  </a-col>-->
                <!--                </a-row>-->
              </div>

              <div v-show="showContent" class="answer">
                <div v-html="childItem.answer"></div>
                <div>
                  {{ childItem.source }}{{ childItem.createdTime
                  }}{{ '正确率:' + Math.round(childItem.correctRatio)
                  }}{{ '易错项:' + childItem.mostWrongAnswer }}
                </div>
                <div v-html="childItem.solution"></div>
                <p></p>
                <div>
                  <dp1 :url="childItem.id" :uid="childItem.id"></dp1>
                  <!--                  {{ console.log(childItem) }}-->
                </div>
              </div>
              <div v-if="index === 1">
                <!-- <p @click="toggleTop" style="color: #00ff05">
                  ================
                </p> -->
                <!-- <div v-html="item.content"></div> -->
                <!--                <p @click="toggleTop" style="color: #00ff05">==============</p>-->
              </div>
            </div>

            <p style="color: #00ff05"></p>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  export default {
    name: 'Min',
    setup() {
      const data = ref([]);
      const isLoading = ref(true);
      const current1 = ref(1);
      const showContent = ref(false);
      const pagetotal = ref(20);
      const pageSize3 = ref(1);
      const showTop = ref(true);
      const total = ref(210);
      const isdangerarr = ref([false, false, false, false]);
      const route = useRoute();
      const fontStyle = ref(16);
      const biaoming = ref('');
      const toggleTop = () => {
        showTop.value = !showTop.value;
      };
      const increaseFontSize = () => {
        fontStyle.value += 2;
        updateFontSize();
      };
      const updateFontSize = () => {
        const wpElement = document.querySelector('.wp');
        wpElement.style.fontSize = `${fontStyle.value}px`;
      };
      const handleKeyDown = async (event) => {
        if (event.key === 'q') {
          if (current1.value > 1) {
            current1.value -= 1;
            await getData();
            if (showContent.value) {
              // ansblack();
              isDanger(false);
              await ansblack();
              showContent.value = false;
            }
          }
        } else if (event.key === 'e') {
          if (current1.value < 10000) {
            current1.value += 1;
            await getData();
            if (showContent.value) {
              // ansblack();
              isDanger(false);
              await ansblack();
              showContent.value = false;
            }
          }
        }
      };

      const right = () => {
        if (current1.value < 10000) {
          current1.value += 1;
          getData();
          ansblack();

          if (showContent.value) {
            isDanger(false);

            showContent.value = false;
          }
        }
      };

      const left = () => {
        if (current1.value > 1) {
          current1.value -= 1;
          getData();
          ansblack();

          if (showContent.value) {
            isDanger(false);

            showContent.value = false;
          }
        }
      };
      const toggleContent = (event) => {
        if (event.key === 'w' || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
          if (showContent.value) {
            // ansblack();
            isDanger(false);
            showContent.value = false;
          } else {
            // ansred();
            const showAnsButton = document.querySelector('.showans');
            showAnsButton.click();
            showContent.value = true;
          }
        }
      };

      const ansred = async () => {
        let clen = 0;
        for (let i = 0; i < data.value.length; i++) {
          for (let j = 0; j < data.value[i].children.length; j++) {
            // console.log(data.value[i].children[j].answer);
            // console.log(clen);
            if (data.value && data.value[i].children[j].answer === 'A') {
              document.getElementsByClassName('an_a')[clen].style.color = 'black';
            }
            if (data.value && data.value[i].children[j].answer === 'B') {
              document.getElementsByClassName('an_b')[clen].style.color = 'black';
            }
            if (data.value && data.value[i].children[j].answer === 'C') {
              document.getElementsByClassName('an_c')[clen].style.color = 'black';
            }
            if (data.value && data.value[i].children[j].answer === 'D') {
              document.getElementsByClassName('an_d')[clen].style.color = 'black';
            }
            if (data.value && data.value[i].children[j].answer === 'A') {
              document.getElementsByClassName('an_a')[clen].style.color = 'red';
            }
            if (data.value && data.value[i].children[j].answer === 'B') {
              document.getElementsByClassName('an_b')[clen].style.color = 'red';
            }
            if (data.value && data.value[i].children[j].answer === 'C') {
              document.getElementsByClassName('an_c')[clen].style.color = 'red';
            }
            if (data.value && data.value[i].children[j].answer === 'D') {
              document.getElementsByClassName('an_d')[clen].style.color = 'red';
            }
            clen = clen + 1;
          }
        }
      };

      const ansblack = async () => {
        let clen = 0;
        for (let i = 0; i < data.value.length; i++) {
          for (let j = 0; j < data.value[i].children.length; j++) {
            // console.log(data.value[i].children[j].answer);
            // console.log(clen);
            if (data.value && data.value[i].children[j].answer === 'A') {
              console.log(i, j, clen, document.getElementsByClassName('an_a')[clen]);
              document.getElementsByClassName('an_a')[clen].style.color = 'black';
            }
            if (data.value && data.value[i].children[j].answer === 'B') {
              console.log(i, j, clen, document.getElementsByClassName('an_b')[clen]);
              document.getElementsByClassName('an_b')[clen].style.color = 'black';
            }
            if (data.value && data.value[i].children[j].answer === 'C') {
              console.log(i, j, clen, document.getElementsByClassName('an_c')[clen]);
              document.getElementsByClassName('an_c')[clen].style.color = 'black';
            }
            if (data.value && data.value[i].children[j].answer === 'D') {
              console.log(i, j, clen, document.getElementsByClassName('an_d')[clen]);
              document.getElementsByClassName('an_d')[clen].style.color = 'black';
            }
            clen = clen + 1;
          }
        }
      };

      const togglean = async (answer) => {
        if (showContent.value) {
          await ansblack();
          isDanger(false);
          showContent.value = false;
        } else {
          await ansblack();
          await ansred();
          isDanger(answer);
          showContent.value = true;
        }
      };

      const pushans = async (answer) => {
        // const url = '/egg/minchoice';
        // data.value.choice = answer;
        // const response = await axios.post(url, data.value);
      };

      const isDanger = (answer) => {
        if (data.value && data.value.referenceAnswer === answer && answer !== false) {
          switch (answer) {
            case 'A':
              isdangerarr.value[0] = true;
              break;
            case 'B':
              isdangerarr.value[1] = true;
              break;
            case 'C':
              isdangerarr.value[2] = true;
              break;
            case 'D':
              isdangerarr.value[3] = true;
              break;
          }
          return true;
        } else if (data.value && answer === false) {
          isdangerarr.value.fill(false);
        }
        return false;
      };

      const getData = async () => {
        const per = route.query.per || pageSize3.value;
        const a = route.query.a || false;
        const id = route.query.id || 48905;
        const page = route.query.page || current1.value;
        const type = route.query.type || 'gwy';
        // const f = new URLSearchParams(window.location.search).get('f');
        const url = '/egg/fbzhiliaoall';
        let params = {
          per: per,
          page: page,
          id: id,
          type: type,
          isfive: 1,
          biao: biaoming.value,
        };
        await ansblack();
        try {
          const response = await axios.get(url, { params });
          // if (response.data.length === 0) {
          //   await getData();
          //   return;
          // }
          if (a) {
            showContent.value = true;
          }

          data.value = response.data;
          for (let item in data.value) {
            // let n = +item + 1;
            // data.value[item].content = data.value[item].content.replace(
            //   '<p>',
            //   '<p>' + n + '.',
            // );

            function isString(value) {
              return typeof value === 'string';
            }

            for (let x in data.value[item].children) {
              // data.value[item].children[x].content = data.value[item].children[
              //   x
              // ].content.replace('<p>', '<p>' + (+x + 1) + '.');
              // data.value[item].children[x].answerone =
              //   'A.' + data.value[item].children[x].answerone;
              // data.value[item].children[x].answertwo =
              //   'B.' + data.value[item].children[x].answertwo;
              // data.value[item].children[x].answerthree =
              //   'C.' + data.value[item].children[x].answerthree;
              // data.value[item].children[x].answerfour =
              //   'D.' + data.value[item].children[x].answerfour;

              try {
                if (
                  isString(data.value[item].children[x].answerone) &&
                  isString(data.value[item].children[x].answertwo) &&
                  isString(data.value[item].children[x].answerthree) &&
                  isString(data.value[item].children[x].answerfour)
                ) {
                  data.value[item].children[x].content = isString(
                    data.value[item].children[x].content,
                  )
                    ? data.value[item].children[x].content.match(/<p>/g)
                      ? data.value[item].children[x].content.replace('<p>', `<p>${+x + 1}.`)
                      : `${+x + 1}.` + data.value[item].children[x].content
                    : data.value[item].children[x].content;

                  data.value[item].children[x].answerone = data.value[item].children[
                    x
                  ].answerone.match(/<p>/g)
                    ? data.value[item].children[x].answerone.replace('<p>', '<p>A.')
                    : 'A.' + data.value[item].children[x].answerone;
                  data.value[item].children[x].answertwo = data.value[item].children[
                    x
                  ].answertwo.match(/<p>/g)
                    ? data.value[item].children[x].answertwo.replace('<p>', '<p>B.')
                    : 'B.' + data.value[item].children[x].answertwo;
                  data.value[item].children[x].answerthree = data.value[item].children[
                    x
                  ].answerthree.match(/<p>/g)
                    ? data.value[item].children[x].answerthree.replace('<p>', '<p>C.')
                    : 'C.' + data.value[item].children[x].answerthree;
                  data.value[item].children[x].answerfour = data.value[item].children[
                    x
                  ].answerfour.match(/<p>/g)
                    ? data.value[item].children[x].answerfour.replace('<p>', '<p>D.')
                    : 'D.' + data.value[item].children[x].answerfour;
                  // if (data.value[item].children[x].answerone.match(/tarzan/g)) {
                  //   data.value[item].children[x].answerone =
                  //     data.value[item].children[x].answerone +
                  //     data.value[item].children[x].answertwo +
                  //     '<pp>' +
                  //     data.value[item].children[x].answerthree +
                  //     data.value[item].children[x].answerfour +
                  //     '</pp>';
                  //   data.value[item].children[x].answertwo = '';
                  //   data.value[item].children[x].answerthree = '';
                  //   data.value[item].children[x].answerfour = '';
                  //   data.value[item].children[x].answerone = data.value[
                  //     item
                  //   ].children[x].answerone
                  //     .replace(/<p>/g, '')
                  //     .replace(/<\/p>/g, '')
                  //     .replace(/<\/pp>/g, '</p>')
                  //     .replace(/<pp>/g, '<p>');
                  // }
                  data.value[item].children[x].answerone = data.value[item].children[x].answerone
                    .replace(/<p>/g, '')
                    .replace(/<\/p>/g, '');
                  data.value[item].children[x].answertwo = data.value[item].children[x].answertwo
                    .replace(/<p>/g, '')
                    .replace(/<\/p>/g, '');
                  data.value[item].children[x].answerthree = data.value[item].children[
                    x
                  ].answerthree
                    .replace(/<p>/g, '')
                    .replace(/<\/p>/g, '');
                  data.value[item].children[x].answerfour = data.value[item].children[x].answerfour
                    .replace(/<p>/g, '')
                    .replace(/<\/p>/g, '');

                  if (
                    +data.value[item].children[x].answerthree.length < 10 ||
                    data.value[item].children[x].answerthree.match('image')
                  ) {
                    data.value[item].children[x].xuanxiang =
                      `<span class="an_a">${data.value[item].children[x].answerone}${'&nbsp;'.repeat(6)}</span><span class="an_b">${data.value[item].children[x].answertwo}</span><span class="an_c">${'&nbsp;'.repeat(6)}${data.value[item].children[x].answerthree}</span><span class="an_d">${'&nbsp;'.repeat(6)}${data.value[item].children[x].answerfour}`;
                  } else if (+data.value[item].children[x].answerthree.length <= 16) {
                    data.value[item].children[x].xuanxiang =
                      `<p><span class="an_a">${data.value[item].children[x].answerone}</span>${'&nbsp;'.repeat(6)}<span class="an_b">${data.value[item].children[x].answertwo}</span></p><p><span class="an_c">${data.value[item].children[x].answerthree}</span>${'&nbsp;'.repeat(6)}<span class="an_d">${data.value[item].children[x].answerfour}</span></p>`;
                  } else {
                    data.value[item].children[x].xuanxiang =
                      `<p class="an_a">${data.value[item].children[x].answerone}</p><p class="an_b">${data.value[item].children[x].answertwo}</p><p class="an_c">${data.value[item].children[x].answerthree}</p><p class="an_d">${data.value[item].children[x].answerfour}</p>`;
                  }
                }

                // console.log(data.value);
              } catch (error) {
                console.error('Error:', error);
              }
            }
          }
          // ansblack();
          // isDanger(false);

          showContent.value = false;
          isLoading.value = false;
        } catch (error) {
          console.error(error);
          isLoading.value = false;
        }
      };

      onMounted(() => {
        document.addEventListener('keydown', toggleContent);
        getData();
        window.addEventListener('keydown', handleKeyDown);
        updateFontSize();
      });

      onBeforeUnmount(() => {
        window.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keydown', toggleContent);
      });

      return {
        data,
        isLoading,
        current1,
        showContent,
        pagetotal,
        pageSize3,
        total,
        left,
        right,
        togglean,
        getData,
        isDanger,
        isdangerarr,
        pushans,
        fontStyle,
        increaseFontSize,
        toggleTop,
        showTop,
        biaoming,
      };
    },
  };
</script>

<style scoped>
  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }
</style>
