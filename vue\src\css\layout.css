/* layout.css - 布局和按钮样式 */

/* 滚动条隐藏 */
.scroll-container::-webkit-scrollbar,
.wp::-webkit-scrollbar {
  display: none; /* Chrome, Safari, and Opera */
}

.scroll-container,
.wp {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* 主要容器样式 */
.wp {
  overflow-x: visible !important;
  overflow-y: auto !important;
  color: var(--theme-color) !important;
  max-width: 100vw;
  word-wrap: break-word;
  width: 100% !important;
  height: 100% !important;
  box-sizing: border-box !important;
  background: var(--theme-bg) !important;
}

.scroll-container {
  overflow-x: visible !important;
  flex: 1;
  overflow-y: auto !important;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scroll-container::-webkit-scrollbar {
  display: none;
}

/* 按钮和控制元素样式 */
.fixed-buttons {
  position: fixed;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.top_button {
  opacity: 0.3;
  background-color: #a8a8a8;
  pointer-events: all;
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  padding: 10px;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.btn {
  width: 60px;
  height: 40px;
  opacity: 0.3;
  background-color: #a8a8a8;
  color: white;
  border: none;
  outline: none;
  cursor: pointer;
  pointer-events: all;
}

/* 顶部输入区域样式 */
.top-input {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}

.top-bottom {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  flex-wrap: nowrap;
}

.items {
  border-left: 2px solid black;
  padding-left: 10px;
}

.lb {
  height: 100%;
  z-index: 1;
}

.rb {
  height: 100%;
  z-index: 1;
}

.main-content-col {
  width: 100% !important;
  height: 100vh !important;
  overflow-y: auto !important;
  overflow-x: clip !important;
  box-sizing: border-box !important;
  background: var(--theme-bg) !important;
  color: var(--theme-color) !important;
}

.main-content-col.no-scroll {
  overflow: hidden !important;
}

/* GRID MODE OVERRIDE */
.desktop-content.no-scroll,
.mobile-content.no-scroll {
  height: 100vh;
  overflow: hidden !important;
  display: flex;
}

.desktop-content.no-scroll .wp,
.mobile-content.no-scroll .wp {
  height: 100%;
  width: 100%;
  overflow: hidden !important;
  display: flex;
}

.desktop-content.no-scroll .datalist,
.mobile-content.no-scroll .datalist {
  display: flex;
  flex: 1;
  width: 100%;
  height: 100%;
}

.desktop-content.no-scroll .datalist > .ant-col,
.mobile-content.no-scroll .datalist > .ant-col {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 桌面端样式 */
@media screen and (min-width: 1200px) {
  .desktop-content {
    display: block;
    width: 100% !important;
    height: 100vh !important;
    overflow-y: auto !important;
    overflow-x: visible !important;
    box-sizing: border-box !important;
    background: var(--theme-bg) !important;
    color: var(--theme-color) !important;
  }

  .desktop-content.no-scroll {
    overflow: hidden;
  }

  .mobile-content {
    display: none;
  }

  .top-input {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 0 0 10px 0;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
  }

  .top-input .ant-btn,
  .top-input .el-button {
    width: 90px;
    min-width: 90px;
    max-width: 120px;
    text-align: center;
    margin-bottom: 4px;
  }

  .top-input .ant-input-number,
  .top-input .el-select {
    width: 90px;
    min-width: 90px;
    max-width: 120px;
  }
}

/* 移动端样式 */
@media screen and (max-width: 1199px) {
  .desktop-content {
    display: none;
  }

  .mobile-content {
    display: flex !important;
    flex-direction: column !important;
    height: 100vh !important;
    width: 100% !important;
    overflow: hidden !important;
    padding: 0 !important;
  }

  .mobile-content .wp {
    flex: 1 !important;
    overflow-y: auto !important;
    overflow-x: visible !important;
    height: 100%;
    padding-top: 2px;
    padding-left: 2px;
    padding-right: 2px;
  }

  /* 当按钮显示时，将顶部空间应用给滚动的 .wp 元素 */
  .mobile-content.show-buttons .wp {
    padding-top: 80px;
  }

  .top-input {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 99;
    background: rgba(255, 255, 255, 0.95);
    padding: 10px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 5px;
  }

  .top-input > * {
    margin: 2px;
  }

  /* 移动端按钮样式优化 */
  .top-input .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    min-height: 28px;
  }

  .top-input .ant-input-number {
    width: 60px;
    font-size: 12px;
  }

  .top-input .el-select {
    width: 80px;
  }

  .top-input .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  /* 移动端隐藏按钮列的容器，只显示固定定位的按钮 */
  .top-input {
    display: block !important;
  }
}

@media only screen and (max-width: 576px) {
  :where(.ant-pagination .ant-pagination-options) {
    display: inline-block !important;
  }

  :where(.css-dev-only-do-not-override-hkh161).ant-pagination {
    width: 100%;
  }

  .rb {
    background-color: rgba(240, 240, 240, 0.3);
  }

  :global(.ant-pagination .ant-pagination-options) {
    display: inline-block !important;
  }

  .top-button {
    display: block;
  }

  .top-input {
    display: block;
  }

  :global(.ant-pagination-options) {
    display: inline-block !important;
  }
}
