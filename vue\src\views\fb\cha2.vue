<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div v-show="showTop" class="top">
          <a-space>
            <a-button @click="toggleTop">隐藏顶部内容</a-button>
            <a-button type="primary" @click="ss()">搜索</a-button>
            <a-button class="showans" @click="togglean()">显示答案</a-button>
            <a-button @click="insertdata()">插入</a-button>
            <a-button @click="toggleInterval">{{
              interstatus ? '关闭定时器' : '开启定时器'
            }}</a-button>
          </a-space>
          <a-space>
            <a-select
              ref="select"
              v-model:value="value1"
              style="width: 120px"
              @focus="focus"
              @change="handleChange()"
            >
              <a-select-option value="48644">gwy</a-select-option>
              <a-select-option value="656604">sy</a-select-option>
              <a-select-option value="783922">gkgwy</a-select-option>
              <a-select-option value="786412">js</a-select-option>
            </a-select>
            <a-input v-model:value="qs" @change="getData()"></a-input>

            <a-input v-model:value="biaoming" placeholder="表名"></a-input>
          </a-space>
        </div>
        <div>
          <div v-html="zltimu.content"></div>
          <div v-for="(item, index) in data" :key="index">
            <div v-if="isLoading">Loading...</div>
            <!--            <fbtag-->
            <!--              :messageFromParent="item.id"-->
            <!--              :messageFromParent1="value1 === 48644 ? 'gwy' : 'sy'"-->
            <!--            ></fbtag>-->
            <div v-if="!isLoading" class="contentx">
              <div v-html="item.content"></div>

              <div class="item" @click="togglean()">
                <p class="an_a" v-html="item.answerone"></p>
                <p class="an_b" v-html="item.answertwo"></p>
                <p class="an_c" v-html="item.answerthree"></p>
                <p class="an_d" v-html="item.answerfour"></p>
              </div>
              <p @click="toggleTop">====================================</p>
              <div v-show="showContent" class="answer">
                <!--              <div v-html="item.answer"></div>-->
                <div @click="open_comment(item.id)">
                  {{ item.source }}{{ item.createdTime
                  }}{{ '正确率：' + Math.round(item.correctRatio) }}
                </div>
                <br />
                <div v-html="item.solution"></div>
                <dp1 :url="+item.id" :uid="+item.id"></dp1>

                <p>====================================</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';
  import { message } from 'ant-design-vue';
  import dp1 from '../../components/dp1.vue';
  const zltimu = ref([]);
  const data = ref([]);
  const isLoading = ref(true);
  const showTop = ref(true);
  const current1 = ref(1);
  const showContent = ref(false);
  const pagetotal = ref(20);
  const pageSize3 = ref(1);
  const total = ref(0);
  const isdangerarr = ref([false, false, false, false]);
  const route = useRoute();
  const fontStyle = ref(16);
  const value1 = ref(48644);
  const timu = ref('');
  const biaoming = ref('');
  const qs = ref('');
  const timuid = ref(0);
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const open_comment = (id) => {
    window.open(
      '/fb/comment?id=' + id,
      '_blank',
      'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=800,height=600',
    );
  };
  const insertdata = async () => {
    const url = '/egg/fbupdatezlfx5000';
    let params = {
      id: timuid.value,
      biao: biaoming.value,
    };
    console.log(params);
    if (biaoming.value === '') {
      message.error({
        content: '表名不能为空',
        duration: 1,
        style: {
          marginTop: '60vh',
        },
      });
      return false;
    }

    try {
      const response = await axios.get(url, { params });
      console.log(response);
      if (response.status === 200) {
        if (response.data.code === 0) {
          message.success({
            content: response.data.message,
            duration: 1,
            style: {
              marginTop: '80vh',
            },
          });
        } else if (response.data.code === 1) {
          message.error({
            content: response.data.message,
            duration: 1,
            style: {
              marginTop: '60vh',
            },
          });
        }
      }
    } catch (error) {
      message.error(error.response.data.message);
    }
  };
  const updateFontSize = () => {
    const wpElement = document.querySelector('.wp');
    wpElement.style.fontSize = `${fontStyle.value}px`;
  };
  const handleKeyDown = (event) => {
    if (event.key === 'ArrowLeft' || event.key === 'q') {
      getData();
    } else if (event.key === 'ArrowRight' || event.key === 'e') {
      insertdata();
    }
  };
  const right = () => {
    if (current1.value < 10000) {
      current1.value += 1;
      getData();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };

  const left = () => {
    if (current1.value > 1) {
      current1.value -= 1;
      getData();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };
  const toggleContent = (event) => {
    if (
      event.key === ' ' ||
      event.key === 'Spacebar' ||
      event.key === 'w' ||
      event.key === 'ArrowUp' ||
      event.key === 'ArrowDown'
    ) {
      if (showContent.value) {
        ansblack();
        isDanger(false);
        showContent.value = false;
      } else {
        ansblack();
        ansred();
        // const showAnsButton = document.querySelector('.showans');
        // showAnsButton.click();
        showContent.value = true;
      }
    }
  };

  const ansred = () => {
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'red';
      }
    }
  };

  const ansblack = () => {
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'black';
      }
    }
  };

  const togglean = (answer) => {
    if (showContent.value) {
      ansblack();
      isDanger(false);
      showContent.value = false;
    } else {
      ansred();
      isDanger(answer);
      showContent.value = true;
    }
  };

  const pushans = async (answer) => {
    // const url = '/egg/minchoice';
    // data.value.choice = answer;
    // const response = await axios.post(url, data.value);
    // console.log(response);
  };
  const copyText = () => {
    let datax = data.value[0];
    console.log(datax);

    // 将<p>和</p>替换为换行符\n，并删除所有其他的HTML标签
    let contentWithoutHtml = datax.content
      .replace(/<p>/gi, '') // 删除开头的<p>标签
      .replace(/<\/p>/gi, '\n') // 将结束的</p>标签替换为换行符
      .replace(/<[^>]*>/g, ''); // 删除所有其他HTML标签

    // 构建最终的文本
    let text = `${contentWithoutHtml}${datax.answerone}\n${datax.answertwo}\n${datax.answerthree}\n${datax.answerfour}\n====================================
      \n`;

    // 将文本复制到剪贴板
    navigator.clipboard.writeText(text);
  };

  const isDanger = (answer) => {
    if (data.value && data.value.answer === answer && answer !== false) {
      switch (answer) {
        case 'A':
          isdangerarr.value[0] = true;
          break;
        case 'B':
          isdangerarr.value[1] = true;
          break;
        case 'C':
          isdangerarr.value[2] = true;
          break;
        case 'D':
          isdangerarr.value[3] = true;
          break;
      }
      return true;
    } else if (data.value && answer === false) {
      isdangerarr.value.fill(false);
    }
    return false;
  };
  const focus = () => {
    console.log('focus');
  };
  const handleChange = async (value) => {
    console.log(`selected ${value}`);
    await getData();
  };
  const ss = async () => {
    qs.value = await navigator.clipboard.readText();
    await getData();
  };
  const getData = async () => {
    const a = route.query.a || false;
    const z = route.query.z || 0;
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/fbcha';
    let clipboardData = await navigator.clipboard.readText();
    console.log('剪贴板', clipboardData);
    console.log('qs', qs.value);
    timu.value = clipboardData;
    if (qs.value) {
      timu.value = qs.value;
      clipboardData = qs.value;
    }
    let params = {
      typeid: value1.value,
      timu: clipboardData,
    };
    console.log(params);
    try {
      await ansblack();
      const response = await axios.get(url, { params });
      if (response.data.length === 0) {
        console.log('z1', z);
        await getData();
        return;
      }
      if (a) {
        showContent.value = true;
      }
      // console.log(response.data.pagetotal[0].total);

      data.value = response.data.data;
      console.log(data.value);
      for (let item in data.value) {
        if (!data.value[item].solution.match(/<p>A/g)) {
          data.value[item].solution = data.value[item].solution.replace(/A项/g, '<br/>A项');
        }
        if (!data.value[item].solution.match(/<p>B/g)) {
          data.value[item].solution = data.value[item].solution.replace(/B项/g, '<br/>B项');
        }
        if (!data.value[item].solution.match(/<p>C/g)) {
          data.value[item].solution = data.value[item].solution.replace(/C项/g, '<br/>C项');
        }
        if (!data.value[item].solution.match(/<p>D/g)) {
          data.value[item].solution = data.value[item].solution.replace(/D项/g, '<br/>D项');
        }
      }
      // total.value = response.data.pagetotal[0].total || 0;
      timuid.value = data.value[0].id;
      showContent.value = false;
      isLoading.value = false;
      await insertdata();
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  const intervalId = ref(null);
  const interstatus = ref(false);
  const createInterval = () => {
    interstatus.value = true;
    intervalId.value = setInterval(getData, 200); // 传递函数引用
  };

  const destroyInterval = () => {
    clearInterval(intervalId.value); // 传递间隔ID
    interstatus.value = false;
  };

  const toggleInterval = () => {
    if (interstatus.value) {
      console.log('1');
      destroyInterval();
    } else {
      console.log('2');
      createInterval();
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', toggleContent);
    // getData();
    window.addEventListener('keydown', handleKeyDown);
    updateFontSize();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
  });
</script>

<style scoped>
  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }

  .taginput {
    user-select: none;
    cursor: pointer;
  }
</style>
