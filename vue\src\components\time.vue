<script>
  import { defineComponent } from 'vue';
  import { ref, onMounted, onUnmounted } from 'vue';
  import dayjs from 'dayjs';

  export default defineComponent({
    name: 'TimeComponent',
    setup() {
      const day = ref(dayjs().format('YYYY-MM-DD HH:mm:ss'));

      let intervalId = null;

      // 在组件加载时启动定时器
      onMounted(() => {
        intervalId = setInterval(() => {
          day.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
        }, 1000); // 每秒钟更新一次day的值
      });

      // 在组件卸载时清除定时器
      onUnmounted(() => {
        if (intervalId) {
          clearInterval(intervalId);
        }
      });

      return {
        day,
      };
    },
  });
</script>

<template>
  <div>{{ day }}</div>
</template>

<style scoped></style>
