<template>
  <div class="flex justify-center items-center min-h-screen bg-gray-100">
    <div class="bg-white border border-gray-200 rounded-lg shadow-md max-w-xl p-8 mx-4">
      <div class="flex flex-col items-center space-y-6">
        <h1 class="text-2xl font-semibold">{{ qrtitle }}</h1>
        <a-qrcode :value="generatedLink" type="svg" class="w-48 h-48" />
        <a-input v-model:value="downloadUrl" placeholder="" class="w-56" allow-clear />
        <a-button type="primary" class="px-6 py-2" @click="copyToClipboard(generatedLink)">
          点击复制
        </a-button>
        <div class="break-words text-center text-blue-500">
          <a :href="generatedLink" target="_blank">
            {{ generatedLink }}
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, computed } from 'vue';
  import Qrcode from 'vue-qrcode';
  import { message } from 'ant-design-vue';
  export default {
    name: 'DownloadLink',
    components: {
      Qrcode,
    },
    setup() {
      const downloadUrl = ref('');
      const qrcodeColor = ref({ dark: '#000', light: '#fff' });
      const qrtitle = ref('二维码生成');

      const generatedLink = computed(() => {
        return downloadUrl.value;
      });
      function copyToClipboard(text) {
        try {
          navigator.clipboard.writeText(text);
          message.success('复制成功!');
        } catch (err) {
          console.error('Failed to copy text: ', err);
          message.warning('复制失败!');
        }
      }
      return {
        downloadUrl,
        generatedLink,
        qrcodeColor,
        copyToClipboard,
        qrtitle,
      };
    },
  };
</script>

<style scoped></style>
