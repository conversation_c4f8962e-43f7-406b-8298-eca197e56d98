<template>
  <a-row>
    <a-col span="4"> </a-col>
    <a-col span="16">
      <div class="button-container">
        <template v-if="!isLoading">
          <template v-for="(subItems, item) in menuItems" :key="item">
            <br />
            <p>{{ item }}{{ refinishedItems['m'][item] }}</p>

            <div v-for="subItem in subItems" :key="subItem">
              <br />
              <a :href="`/egg/mintimu?per=2&f=${subItem.firstKnowledgeName}`" target="_blank">
                <a-button type="primary">
                  {{ subItem.firstKnowledgeName
                  }}{{ refinishedItems['f'][subItem.firstKnowledgeName] }}
                </a-button>
              </a>
              <br />
              <br />
              <a
                v-for="item in subItem.secondKnowledgeName"
                :key="item"
                :href="`/egg/mintimu?per=2&s=${item}`"
                target="_blank"
              >
                <a-button> {{ item }}{{ refinishedItems['s'][item] }} </a-button>
              </a>
            </div>
          </template>
        </template>
        <p v-else>Loading...</p>
      </div>
    </a-col>
    <a-col span="4"></a-col>
  </a-row>
</template>

<script>
  import { onMounted, ref } from 'vue';
  import { Button } from 'ant-design-vue';
  import axios from 'axios';

  export default {
    name: 'Minnav',
    components: {
      'a-button': Button,
    },
    setup() {
      const menuItems = ref([]);
      const isLoading = ref(true);
      const refinishedItems = ref([]);

      const finish = () => {
        let data = menuItems.value;
        // console.log(data);

        for (let item in data) {
          if (item || !refinishedItems.value['m'][item]) {
            refinish(item, 'm');
          }
          for (let subItem of data[item]) {
            if (
              subItem.firstKnowledgeName ||
              !refinishedItems.value['f'][subItem.firstKnowledgeName] ||
              !subItem.firstKnowledgeName ||
              subItem.firstKnowledgeName !== null ||
              subItem.firstKnowledgeName !== undefined
            ) {
              refinish(subItem.firstKnowledgeName, 'f');
            }
            for (let subsubItem of subItem.secondKnowledgeName) {
              if (subsubItem || !refinishedItems.value['s'][subsubItem] || !subsubItem) {
                refinish(subsubItem, 's');
              }
            }
          }
        }
        return;
      };

      const refinish = (text, m) => {
        if (refinishedItems.value[text] || text === '' || text === undefined || text === null) {
          return '';
        }

        // console.log(text + "请求开始");
        const url = '/egg/minrefinish';
        const params = {
          text: text,
          m: m,
        };

        if (text === '') {
          return;
        }
        refinishedItems.value[m] = [];
        try {
          axios.get(url, { params }).then((res) => {
            refinishedItems.value[m][text] = res.data.st;
            // console.log(refinishedItems.value[m]);
            // console.log(text + "请求完成");
          });
        } catch (error) {
          console.error(error);
        }
      };

      const getData = async () => {
        const url = '/egg/minnav1';
        const params = {};

        try {
          const response = await axios.get(url, { params });

          const cleanedData = {};

          Object.keys(response.data).forEach((category) => {
            const categoryData = response.data[category].filter((item) => {
              return item.firstKnowledgeName !== '' && item.secondKnowledgeName.length > 0;
            });

            if (categoryData.length > 0) {
              cleanedData[category] = categoryData;
            }
          });
          menuItems.value = cleanedData;
          // console.log(menuItems.value);
          isLoading.value = false;
          // console.log(menuItems.value);

          finish();
        } catch (error) {
          console.error(error);
        }
      };

      onMounted(async () => {
        await getData();
      });

      return {
        menuItems,
        isLoading,
        refinish,
        refinishedItems,
        finish,
      };
    },
  };
</script>

<style scoped>
  .button-container {
    text-align: center;
    margin-top: 20px;
  }
</style>
