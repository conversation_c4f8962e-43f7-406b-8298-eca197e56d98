{"name": "egg", "version": "1.0.0", "description": "egg", "private": true, "egg": {"declarations": true}, "dependencies": {"@alicloud/pop-core": "^1.8.0", "@fastify/cookie": "^9.4.0", "@fastify/session": "^10.9.0", "axios": "1.8.2", "base64url": "^3.0.1", "cheerio": "1.0.0", "dayjs": "^1.11.13", "dns-packet": "^5.6.1", "egg": "^3.17.5", "egg-cors": "^3.0.1", "egg-mysql": "^4.1.0", "egg-redis": "^2.6.1", "egg-scripts": "2", "egg-socket.io": "^4.1.6", "egg-view-ejs": "^3.0.0", "egg-view-nunjucks": "^2.3.0", "eventsource-parser": "^3.0.2", "form-data": "^4.0.4", "https-proxy-agent": "^7.0.6", "iconv-lite": "^0.6.3", "ip-range-check": "^0.2.0", "json5": "2.2.3", "lodash": "^4.17.21", "mkdirp": "^3.0.1", "openai": "^5.0.1", "puppeteer": "^24.9.0", "sharp": "^0.34.2", "tencentcloud-sdk-nodejs": "^4.1.23", "uuid": "^11.0.5", "yaml": "^2.7.0"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/preset-env": "^7.26.7", "@types/node": "^22.10.10", "@typescript-eslint/eslint-plugin": "~6.21.0", "@typescript-eslint/parser": "~6.21.0", "autod": "^3.1.2", "autod-egg": "^1.1.0", "chalk": "^4.1.2", "cli-spinners": "^3.2.0", "concurrently": "^9.2.0", "core-js": "3", "egg-bin": "^6.13.0", "egg-ci": "^2.2.0", "egg-mock": "^5.15.1", "eslint": "^8.57.1", "eslint-config-egg": "^13.1.0", "gradient-string": "^3.0.0", "ora": "^8.1.1", "prettier": "^3.4.2"}, "engines": {"node": ">=10.0.0"}, "scripts": {"dev": "\"powershell -ExecutionPolicy Bypass -File ./kill-port.ps1 -Port 7001 -Name Egg && pnpm dev:new --port 7001\"", "dev:all": "concurrently -k -r -s first -n \"Vue,Egg\" -c \"cyan.bold,magenta.bold\" -t \"HH:mm:ss\" \"powershell -ExecutionPolicy Bypass -File ./kill-port.ps1 -Port 6001 -Name Vue && cd ./vue && pnpm dev --port 6001\" \"powershell -ExecutionPolicy Bypass -File ./kill-port.ps1 -Port 7001 -Name Egg && pnpm dev --port 7001\"", "dev:new": "pnpm exec egg-bin dev --workers=1 --sticky=false", "start": "pnpm exec egg-scripts start --daemon --title=egg", "stop": "pnpm exec egg-scripts stop --title=egg", "restart": "pnpm stop && sleep 2 && pnpm start", "lint": "pnpm exec prettier . --write"}, "ci": {"version": "10"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT"}