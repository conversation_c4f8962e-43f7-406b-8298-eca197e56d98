import axios from 'axios';

/**
 * 画布工具类
 * 提供画布初始化、绘制、保存、加载等功能
 */
export class CanvasManager {
  constructor() {
    this.ctx = null;
    this.drawing = false;
    this.canvasStrokes = []; // 存储画布笔画数据
    this.canvasSettings = {
      strokeColor: '#0000FF',
      strokeWidth: 2, // 增加默认线宽，减少锯齿
      eraseSize: 10,
    };
    this.saveTimeout = null; // 保存防抖定时器
    this.eraseSize = 30; // 擦除范围大小
    this.showEraseIndicator = true; // 默认显示擦除范围指示器
    this.mouseX = 0; // 鼠标X坐标
    this.mouseY = 0; // 鼠标Y坐标
    this.penPressure = 0; // 笔的压力值
    this.penTiltX = 0; // 笔的X轴倾斜
    this.penTiltY = 0; // 笔的Y轴倾斜
    this.penSupported = false; // 是否支持数位板笔
    this.isEraseMode = false; // 是否处于擦除模式
    this.currentId = ''; // 当前题目ID
    this.devicePixelRatio = window.devicePixelRatio || 1; // 设备像素比
    this.lastPoint = null; // 上一个绘制点，用于平滑绘制
    this.lastDrawPoint = null; // 上一个实际绘制点，用于贝塞尔曲线
    this.isInitialized = false; // 🔥 防止重复初始化标志
    this.lastToggleTime = 0; // 上次模式切换时间，用于防抖
  }

  /**
   * 设置高DPI支持，减少锯齿
   * @param {HTMLCanvasElement} canvas - 画布元素
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  setupHighDPI(canvas, ctx) {
    const dpr = this.devicePixelRatio;

    if (dpr > 1) {
      // 获取当前画布尺寸
      const currentWidth = canvas.width;
      const currentHeight = canvas.height;

      // 设置实际像素尺寸（放大）
      canvas.width = currentWidth * dpr;
      canvas.height = currentHeight * dpr;

      // 设置CSS显示尺寸（保持原始大小）
      canvas.style.width = currentWidth + 'px';
      canvas.style.height = currentHeight + 'px';

      // 缩放上下文以匹配设备像素比
      ctx.scale(dpr, dpr);
    }
  }

  /**
   * 获取平滑的坐标点
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @returns {Object} 平滑后的坐标
   */
  getSmoothPoint(x, y) {
    if (!this.lastPoint) {
      this.lastPoint = { x, y };
      return { x, y };
    }

    // 使用简单的平滑算法
    const smoothFactor = 0.3;
    const smoothX = this.lastPoint.x + (x - this.lastPoint.x) * smoothFactor;
    const smoothY = this.lastPoint.y + (y - this.lastPoint.y) * smoothFactor;

    this.lastPoint = { x: smoothX, y: smoothY };
    return { x: smoothX, y: smoothY };
  }

  /**
   * 绘制平滑线条 - 重构版本，智能处理擦除和绘制模式
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  drawSmoothLine(ctx, x, y) {
    // 保存当前状态
    ctx.save();

    // 设置高质量绘制参数
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    ctx.miterLimit = 10;

    // 🎯 根据模式设置不同的绘制策略
    if (this.isEraseMode) {
      // 擦除模式：使用直线绘制确保擦除效果
      ctx.globalCompositeOperation = 'destination-out';
      ctx.lineWidth = this.eraseSize;

      if (this.lastDrawPoint && this.drawing) {
        // 连续擦除：直线连接
        ctx.beginPath();
        ctx.moveTo(this.lastDrawPoint.x, this.lastDrawPoint.y);
        ctx.lineTo(x, y);
        ctx.stroke();
      } else {
        // 开始擦除：绘制圆点
        ctx.beginPath();
        ctx.arc(x, y, this.eraseSize / 2, 0, 2 * Math.PI);
        ctx.fill();
      }
    } else {
      // 绘制模式：使用平滑曲线
      ctx.globalCompositeOperation = 'source-over';
      ctx.strokeStyle = this.canvasSettings.strokeColor;
      ctx.lineWidth = Math.max(1.5, this.canvasSettings.strokeWidth);

      if (this.lastDrawPoint && this.drawing) {
        // 连续绘制：贝塞尔曲线平滑
        const midX = (this.lastDrawPoint.x + x) / 2;
        const midY = (this.lastDrawPoint.y + y) / 2;

        ctx.beginPath();
        ctx.moveTo(this.lastDrawPoint.x, this.lastDrawPoint.y);
        ctx.quadraticCurveTo(this.lastDrawPoint.x, this.lastDrawPoint.y, midX, midY);
        ctx.stroke();

        // 为下次绘制准备
        ctx.beginPath();
        ctx.moveTo(midX, midY);
      } else {
        // 开始绘制：绘制起始点
        ctx.beginPath();
        ctx.arc(x, y, ctx.lineWidth / 2, 0, 2 * Math.PI);
        ctx.fill();
        ctx.beginPath();
        ctx.moveTo(x, y);
      }
    }

    // 更新最后绘制点
    this.lastDrawPoint = { x, y };

    // 恢复状态
    ctx.restore();
  }

  /**
   * 初始化画布
   * @param {string} canvasId - 画布元素ID
   * @param {string} indicatorCanvasId - 指示器画布元素ID
   * @param {string} containerSelector - 容器选择器
   */
  async initCanvas(
    canvasId = 'drawingCanvas',
    indicatorCanvasId = 'indicatorCanvas',
    containerSelector = '.wp',
  ) {
    console.log('🎨 开始初始化画布...');

    // 🔥 防止重复初始化
    if (this.isInitialized) {
      console.log('⚠️ 画布已经初始化，跳过重复初始化');
      return;
    }

    const canvas = document.getElementById(canvasId);
    const indicatorCanvas = document.getElementById(indicatorCanvasId);

    if (!canvas) {
      console.warn(`⚠️ 画布元素暂未找到: ${canvasId}，可能DOM还未准备好`);
      // 🔥 重试前检查是否已经初始化，防止无限重试
      if (!this.isInitialized) {
        console.log('🔄 重试初始化画布...');
        setTimeout(() => {
          this.initCanvas(canvasId, indicatorCanvasId, containerSelector);
        }, 200);
      } else {
        console.log('⚠️ 画布已初始化，跳过重试');
      }
      return;
    }

    const container = document.querySelector(containerSelector);
    if (!container) {
      console.error('❌ 找不到容器元素:', containerSelector);
      return;
    }

    // 等待容器尺寸稳定的Promise版本
    await new Promise((resolve) => {
      const checkSize = () => {
        const containerWidth = container.offsetWidth;
        const containerHeight = container.offsetHeight;

        if (containerWidth > 0 && containerHeight > 0) {
          console.log(`📏 容器尺寸: ${containerWidth}x${containerHeight}`);

          // 画布大小应该和.wp区域一样大，右边减少60px
          canvas.width = containerWidth - 60;
          canvas.height = containerHeight;

          // 设置指示器画布尺寸
          if (indicatorCanvas) {
            indicatorCanvas.width = containerWidth - 60;
            indicatorCanvas.height = containerHeight;
          }

          // 强制重绘
          canvas.style.display = 'none';
          canvas.offsetHeight; // 触发重排
          canvas.style.display = '';

          if (indicatorCanvas) {
            indicatorCanvas.style.display = 'none';
            indicatorCanvas.offsetHeight; // 触发重排
            indicatorCanvas.style.display = '';
          }

          resolve();
        } else {
          // 如果尺寸还没准备好，等待一下再检查
          setTimeout(checkSize, 50);
        }
      };

      // 立即检查一次，如果不行就等待
      setTimeout(checkSize, 0);
    });

    // 初始化主画布
    this.ctx = canvas.getContext('2d', {
      alpha: true,
      desynchronized: true, // 提高性能
      willReadFrequently: false, // 优化写入性能
    });

    // 高质量抗锯齿和平滑处理
    this.ctx.imageSmoothingEnabled = true;
    this.ctx.imageSmoothingQuality = 'high';

    // 优化线条绘制质量
    this.ctx.lineCap = 'round';
    this.ctx.lineJoin = 'round';
    this.ctx.globalCompositeOperation = 'source-over';
    this.ctx.strokeStyle = this.canvasSettings.strokeColor;
    this.ctx.lineWidth = this.canvasSettings.strokeWidth;

    // 支持高DPI显示
    this.setupHighDPI(canvas, this.ctx);

    // 设置画布的触摸行为
    this.setCanvasTouchBehavior(canvas);

    // 初始化指示器画布
    if (indicatorCanvas) {
      this.indicatorCtx = indicatorCanvas.getContext('2d');

      // 设置指示器画布的样式
      this.indicatorCtx.strokeStyle = '#ff0000';
      this.indicatorCtx.lineWidth = 2;
      this.indicatorCtx.setLineDash([5, 5]);

      // 设置指示器画布的触摸行为
      this.setCanvasTouchBehavior(indicatorCanvas);
    }

    // 清除之前的事件监听器
    this.removeEventListeners(canvas);
    if (indicatorCanvas) {
      this.removeEventListeners(indicatorCanvas);
    }

    // 添加事件监听器
    this.addEventListeners(canvas);
    if (indicatorCanvas) {
      this.addEventListeners(indicatorCanvas);
    }

    // 检测是否支持数位板笔
    if ('PointerEvent' in window) {
      this.penSupported = true;
      console.log('✅ 支持指针事件，数位板笔可用');
    } else {
      console.log('⚠️ 不支持指针事件，数位板笔不可用');
    }

    console.log(`✅ 画布初始化完成: ${canvas.width}x${canvas.height}`);

    // 🔥 设置初始化标志，防止重复初始化
    this.isInitialized = true;

    // 🔥 修复：移除自动加载逻辑，画布数据应该只在用户主动打开画布时加载
    console.log('💡 画布初始化完成，等待用户主动打开画布后再加载数据');
  }

  /**
   * 设置画布的触摸行为
   * @param {HTMLCanvasElement} canvas - 画布元素
   */
  setCanvasTouchBehavior(canvas) {
    canvas.style.touchAction = 'none';
    canvas.style.webkitTouchCallout = 'none';
    canvas.style.webkitUserSelect = 'none';
    canvas.style.khtmlUserSelect = 'none';
    canvas.style.mozUserSelect = 'none';
    canvas.style.msUserSelect = 'none';
    canvas.style.userSelect = 'none';
  }

  /**
   * 🎧 添加事件监听器
   * @param {HTMLCanvasElement} canvas - 画布元素
   */
  addEventListeners(canvas) {
    if (!canvas) return;

    // 创建绑定的方法引用（用于后续移除）- 只绑定存在的方法
    if (!this._boundMethods) {
      this._boundMethods = {};

      // 安全绑定方法，只绑定存在的方法
      const methodsToCheck = [
        'startDrawing',
        'draw',
        'stopDrawing',
        'updateMousePosition',
        'handleMouseDown',
        'handleMouseMove',
        'handleMouseUp',
        'handleTouchStart',
        'drawTouch',
        'handleTouchEnd',
        'handlePointerDown',
        'handlePointerMove',
        'handlePointerUp',
      ];

      methodsToCheck.forEach((methodName) => {
        if (typeof this[methodName] === 'function') {
          this._boundMethods[methodName] = this[methodName].bind(this);
        }
      });
    }

    // 鼠标事件 - 使用存在的方法
    if (this._boundMethods.handleMouseDown) {
      canvas.addEventListener('mousedown', this._boundMethods.handleMouseDown);
    }
    if (this._boundMethods.handleMouseMove) {
      canvas.addEventListener('mousemove', this._boundMethods.handleMouseMove);
    }
    if (this._boundMethods.handleMouseUp) {
      canvas.addEventListener('mouseup', this._boundMethods.handleMouseUp);
    }

    // 触摸事件 - 使用存在的方法
    if (this._boundMethods.handleTouchStart) {
      canvas.addEventListener('touchstart', this._boundMethods.handleTouchStart, {
        passive: false,
      });
    }
    if (this._boundMethods.drawTouch) {
      canvas.addEventListener('touchmove', this._boundMethods.drawTouch, { passive: false });
    }
    if (this._boundMethods.handleTouchEnd) {
      canvas.addEventListener('touchend', this._boundMethods.handleTouchEnd, { passive: false });
    }

    // 指针事件（数位笔支持）- 使用存在的方法
    if ('PointerEvent' in window) {
      if (this._boundMethods.handlePointerDown) {
        canvas.addEventListener('pointerdown', this._boundMethods.handlePointerDown);
      }
      if (this._boundMethods.handlePointerMove) {
        canvas.addEventListener('pointermove', this._boundMethods.handlePointerMove);
      }
      if (this._boundMethods.handlePointerUp) {
        canvas.addEventListener('pointerup', this._boundMethods.handlePointerUp);
      }
    }

    console.log('✅ 事件监听器已添加到画布:', canvas.id);
  }

  /**
   * 🔇 移除事件监听器
   * @param {HTMLCanvasElement} canvas - 画布元素
   */
  removeEventListeners(canvas) {
    if (!canvas || !this._boundMethods) return;

    // 鼠标事件 - 安全移除
    if (this._boundMethods.handleMouseDown) {
      canvas.removeEventListener('mousedown', this._boundMethods.handleMouseDown);
    }
    if (this._boundMethods.handleMouseMove) {
      canvas.removeEventListener('mousemove', this._boundMethods.handleMouseMove);
    }
    if (this._boundMethods.handleMouseUp) {
      canvas.removeEventListener('mouseup', this._boundMethods.handleMouseUp);
    }

    // 触摸事件 - 安全移除
    if (this._boundMethods.handleTouchStart) {
      canvas.removeEventListener('touchstart', this._boundMethods.handleTouchStart);
    }
    if (this._boundMethods.drawTouch) {
      canvas.removeEventListener('touchmove', this._boundMethods.drawTouch);
    }
    if (this._boundMethods.handleTouchEnd) {
      canvas.removeEventListener('touchend', this._boundMethods.handleTouchEnd);
    }

    // 指针事件 - 安全移除
    if ('PointerEvent' in window) {
      if (this._boundMethods.handlePointerDown) {
        canvas.removeEventListener('pointerdown', this._boundMethods.handlePointerDown);
      }
      if (this._boundMethods.handlePointerMove) {
        canvas.removeEventListener('pointermove', this._boundMethods.handlePointerMove);
      }
      if (this._boundMethods.handlePointerUp) {
        canvas.removeEventListener('pointerup', this._boundMethods.handlePointerUp);
      }
    }

    console.log('✅ 事件监听器已从画布移除:', canvas.id);
  }

  /**
   * 清除画布
   */
  async clearCanvas() {
    // 清除画布
    if (this.ctx) {
      this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
    }

    this.canvasStrokes = []; // 清空笔画数据

    // 立即保存清空后的状态
    await this.saveCanvasData(true);
  }

  /**
   * 保存canvas数据到数据库（带防抖）
   * @param {boolean} immediate - 是否立即保存
   */
  async saveCanvasData(immediate = false) {
    // 清除之前的定时器
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    // 如果不是立即保存，使用防抖
    if (!immediate) {
      this.saveTimeout = setTimeout(async () => {
        await this.performSave();
      }, 500); // 500ms防抖
      return;
    }

    // 立即保存
    await this.performSave();
  }

  /**
   * 🎨 智能点采样 - 保持画笔完整性的优化版本
   * @param {Array} points - 原始点数组
   * @param {number} maxPoints - 最大保留点数 (15-25个，保持绘制质量)
   */
  samplePoints(points, maxPoints = 20) {
    if (points.length <= maxPoints) {
      return points;
    }

    const sampled = [];

    // 🎯 总是保留起点和终点
    sampled.push(points[0]);

    if (maxPoints > 2) {
      // 🧠 智能采样策略：保留关键转折点
      const step = (points.length - 1) / (maxPoints - 1);

      for (let i = 1; i < maxPoints - 1; i++) {
        const index = Math.round(i * step);
        const point = points[index];

        // 保留完整的点信息（包括压感、倾斜等）
        sampled.push({
          x: Math.round(point.x * 10) / 10, // 保留1位小数精度
          y: Math.round(point.y * 10) / 10,
          time: point.time,
          pressure: point.pressure || 1.0, // 保留压感信息
          tiltX: point.tiltX || 0, // 保留倾斜信息
          tiltY: point.tiltY || 0,
          twist: point.twist || 0, // 保留旋转信息
        });
      }
    }

    sampled.push(points[points.length - 1]); // 终点

    return sampled;
  }

  /**
   * 🖊️ 检测设备类型
   */
  getDeviceType() {
    // 检测是否支持数位笔
    if (navigator.maxTouchPoints > 1) {
      return 'touch'; // 触摸设备
    }
    if (window.PointerEvent) {
      return 'pen'; // 支持指针事件，可能是数位笔
    }
    return 'mouse'; // 鼠标设备
  }

  /**
   * 检查数据大小并进一步压缩
   * @param {Object} data - 要检查的数据
   * @param {number} maxSize - 最大允许大小（字节）
   */
  compressIfNeeded(data, maxSize = 500000) {
    // 500KB限制
    let jsonString = JSON.stringify(data);

    if (jsonString.length <= maxSize) {
      return data;
    }

    console.log(`数据过大 (${jsonString.length} bytes)，进行进一步压缩...`);

    // 进一步减少采样点
    const compressedData = {
      ...data,
      s: data.s.map((stroke) => ({
        ...stroke,
        p: this.samplePoints(
          stroke.p.map((p) => ({ x: p[0], y: p[1] })),
          3,
        ).map((p) => [p.x, p.y]),
      })),
    };

    jsonString = JSON.stringify(compressedData);
    console.log(`压缩后大小: ${jsonString.length} bytes`);

    return compressedData;
  }

  /**
   * 实际执行保存的函数
   */
  async performSave() {
    try {
      const canvas = document.getElementById('drawingCanvas');
      if (!canvas || !this.currentId) {
        console.log('没有画布或当前题目ID，跳过保存');
        return;
      }

      // 🎯 超级压缩画布数据，大幅减少数据量
      const compressedStrokes = this.canvasStrokes.map((stroke, index) => {
        // 🎨 智能点采样，保持画笔完整性 - 提高质量
        const sampledPoints = this.samplePoints(stroke.points, 20); // 保留20个关键点，提高绘制质量

        const baseStroke = {
          t: stroke.type === 'erase' ? 1 : 0, // 类型标识
          w:
            Math.round(
              (stroke.width ||
                (stroke.type === 'erase' ? this.eraseSize : this.canvasSettings.strokeWidth)) * 10,
            ) / 10,
          p: sampledPoints.map((point) => {
            // 🎯 保留完整的点信息数组格式
            const pointData = [
              Math.round(point.x * 10) / 10, // x坐标 (1位小数)
              Math.round(point.y * 10) / 10, // y坐标 (1位小数)
            ];

            // 🖊️ 保留数位笔的高级属性（如果存在）
            if (point.pressure && point.pressure !== 1.0) {
              pointData.push(Math.round(point.pressure * 100) / 100); // 压感 (2位小数)
            }
            if (point.tiltX || point.tiltY) {
              pointData.push(Math.round(point.tiltX || 0)); // 倾斜X
              pointData.push(Math.round(point.tiltY || 0)); // 倾斜Y
            }

            return pointData;
          }),
          ts: stroke.startTime || Date.now(), // 开始时间戳
        };

        // 只有非默认颜色才保存颜色信息
        if (stroke.color && stroke.color !== this.canvasSettings.strokeColor) {
          baseStroke.c = stroke.color;
        }

        return baseStroke;
      });

      // 🎨 v4.0数据格式 - 平衡压缩率和画笔完整性
      let canvasData = {
        v: 4, // 版本号4，支持完整画笔信息
        s: compressedStrokes,
        // 保存必要的设置
        es: this.eraseSize, // 擦除大小
        meta: {
          device: this.getDeviceType(), // 设备类型
          quality: 'high', // 质量等级
          timestamp: Date.now(),
        },
      };

      // 检查数据大小并进一步压缩
      canvasData = this.compressIfNeeded(canvasData);

      const dataString = JSON.stringify(canvasData);
      console.log(
        `📦 准备保存数据大小: ${dataString.length} bytes (${(dataString.length / 1024).toFixed(2)} KB)`,
      );

      const response = await axios.post('/egg/canvas', {
        id: this.currentId,
        canvas_data: dataString,
        canvas_width: canvas.width,
        canvas_height: canvas.height,
        has_canvas: this.canvasStrokes.length > 0 ? 1 : 0,
      });

      if (response.status === 200) {
        console.log('✅ 画布数据保存成功');

        // 🚀 恢复：触发增量传输 - 单用户模式下用于增量保存
        this.triggerRealtimeUpdate(compressedStrokes);
      } else {
        console.error('❌ 保存画布数据失败');
      }
    } catch (error) {
      console.error('保存画布数据出错:', error);
    }
  }

  /**
   * 从数据库读取canvas数据 - 增强版本
   */
  async loadCanvasData() {
    try {
      if (!this.currentId) {
        console.log('⚠️ 没有当前题目ID，跳过加载canvas数据');
        return;
      }

      console.log(`🔄 开始加载题目 ${this.currentId} 的画布数据...`);

      const response = await axios.get('/egg/canvas', {
        params: { id: this.currentId },
      });

      if (response.status !== 200) {
        console.error(`❌ 加载画布数据失败: HTTP ${response.status}`);
        return;
      }

      const data = response.data;
      console.log('📦 收到画布数据:', data);

      if (data.has_canvas && data.canvas_data) {
        const canvasData = JSON.parse(data.canvas_data);

        // 恢复桌面端画布尺寸 - 但要确保符合.wp区域大小，右边减少60px
        const canvas = document.getElementById('drawingCanvas');
        const indicatorCanvas = document.getElementById('indicatorCanvas');
        if (canvas) {
          const container = document.querySelector('.wp');
          if (container) {
            // 使用当前容器尺寸，而不是数据库中保存的尺寸
            canvas.width = container.offsetWidth - 60;
            canvas.height = container.offsetHeight;
            // 同时更新指示器画布
            if (indicatorCanvas) {
              indicatorCanvas.width = container.offsetWidth - 60;
              indicatorCanvas.height = container.offsetHeight;
            }
          }
        }

        // 移动端不需要画布功能，已移除相关代码

        // 🎯 智能解压绘制数据，支持多版本格式 - 修复版本检测
        const version = canvasData.v || canvasData.version || 1;
        console.log(`加载画布数据版本: ${version}`);

        let decompressedStrokes = [];

        // 🔥 关键修复：正确处理不同版本的数据结构
        if (version >= 4) {
          // v4.0格式：使用s数组
          decompressedStrokes = (canvasData.s || []).map((stroke, index) => {
            const baseStroke = {
              type: stroke.t === 1 ? 'erase' : 'draw',
              color: stroke.c || this.canvasSettings.strokeColor,
              width: stroke.w || this.canvasSettings.strokeWidth,
              points: (stroke.p || []).map((point, i) => ({
                x: point[0],
                y: point[1],
                pressure: point[2] || 0.5,
                tiltX: point[3] || 0,
                tiltY: point[4] || 0,
                time: stroke.ts + i || Date.now(),
              })),
              startTime: stroke.ts || Date.now(),
              endTime: stroke.ts || Date.now(),
              duration: 0,
            };
            return baseStroke;
          });

          // 恢复画布设置
          if (canvasData.es) {
            this.eraseSize = canvasData.es;
          }
        } else if (version >= 3) {
          // 新的超级压缩格式
          decompressedStrokes = (canvasData.s || []).map((stroke, index) => {
            const baseStroke = {
              type: stroke.t === 1 ? 'erase' : 'draw', // 数字转换回类型
              color: stroke.c || this.canvasSettings.strokeColor,
              width: stroke.w || this.canvasSettings.strokeWidth,
              points: (stroke.p || []).map((point, i) => ({
                x: point[0], // 数组格式的坐标
                y: point[1],
                time: Date.now() + i, // 简单的时间戳
              })),
              startTime: Date.now(),
              endTime: Date.now(),
              duration: 0,
              originalIndex: index,
            };

            if (stroke.t === 1) {
              baseStroke.isErase = true;
            }

            return baseStroke;
          });

          // 恢复擦除大小
          if (canvasData.es) {
            this.eraseSize = canvasData.es;
          }
        } else {
          // 兼容旧格式
          decompressedStrokes = (canvasData.s || []).map((stroke, index) => {
            const baseStroke = {
              type: stroke.t || 'draw',
              color: stroke.c || this.canvasSettings.strokeColor,
              width: stroke.w || this.canvasSettings.strokeWidth,
              points: (stroke.p || []).map((point) => ({
                x: point.x,
                y: point.y,
                time: point.t,
              })),
              startTime: stroke.s,
              endTime: stroke.e,
              duration: stroke.d,
              originalIndex: stroke.i !== undefined ? stroke.i : index,
            };

            if (stroke.t === 'erase' || stroke.isErase) {
              baseStroke.type = 'erase';
              baseStroke.isErase = true;
              baseStroke.width = stroke.eraseSize || stroke.w || this.eraseSize;
            }

            return baseStroke;
          });

          // 恢复画布设置（旧格式）
          const savedSettings = canvasData.st || {};
          if (savedSettings.eraseSize) {
            this.eraseSize = savedSettings.eraseSize;
          }
        }

        this.canvasStrokes = decompressedStrokes;

        // 注意：不自动恢复擦除模式状态，保持用户当前选择
        console.log(
          `恢复了 ${decompressedStrokes.length} 个笔画，其中擦除笔画 ${decompressedStrokes.filter((s) => s.type === 'erase').length} 个`,
        );

        // 🚀 关键修复：智能延迟重绘，确保DOM完全准备好
        console.log(`📋 准备重绘画布: ${decompressedStrokes.length} 个笔画`);
        this.smartRedraw(decompressedStrokes.length);
      } else {
        console.log('📭 没有找到已保存的canvas数据，画布为空');
        // 清空画布以确保干净状态
        this.canvasStrokes = [];
        if (this.ctx) {
          this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        }
      }
    } catch (error) {
      console.error('❌ 恢复画布数据失败:', error);
      // 发生错误时也要清空画布
      this.canvasStrokes = [];
      if (this.ctx) {
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
      }
    }
  }

  /**
   * 🚀 智能延迟重绘 - 确保DOM完全准备好
   */
  smartRedraw(strokeCount, attempt = 1, maxAttempts = 10) {
    console.log(`🎯 智能重绘尝试 ${attempt}/${maxAttempts}，笔画数: ${strokeCount}`);

    const canvas = document.getElementById('drawingCanvas');
    if (!canvas) {
      if (attempt < maxAttempts) {
        console.log(`⏰ 画布元素未找到，${100 * attempt}ms后重试...`);
        setTimeout(() => {
          this.smartRedraw(strokeCount, attempt + 1, maxAttempts);
        }, 100 * attempt); // 递增延迟：100ms, 200ms, 300ms...
      } else {
        console.error(`❌ 达到最大重试次数，画布元素仍未找到`);
      }
      return;
    }

    console.log(`✅ 找到画布元素，开始重绘...`);

    // 🔥 终极修复：直接使用forceRedraw，确保重绘执行
    const self = this;
    setTimeout(() => {
      console.log(`🎨 开始延迟重绘...`);
      console.log('🔥 直接调用强制重绘方法...');
      self.forceRedraw();
      console.log(`✅ 画布数据恢复成功: ${strokeCount} 个笔画`);
    }, 50); // 短暂延迟确保DOM完全准备
  }

  /**
   * 🚀 强制重绘 - 备用重绘方案
   */
  forceRedraw() {
    console.log('🔥 执行强制重绘...');
    const canvas = document.getElementById('drawingCanvas');
    if (!canvas) {
      console.error('❌ 强制重绘失败：找不到画布元素');
      return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('❌ 强制重绘失败：无法获取画布上下文');
      return;
    }

    console.log(`🎨 强制重绘开始: ${this.canvasStrokes.length} 个笔画`);
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (this.canvasStrokes.length > 0) {
      this.drawStrokesToCanvas(ctx);
    }

    console.log('✅ 强制重绘完成');
  }

  /**
   * 重新绘制canvas - 桌面端专用版本，确保上下文正确初始化
   */
  redrawCanvas() {
    console.log('🔄 redrawCanvas 被调用');

    // 只处理桌面端画布
    const desktopCanvas = document.getElementById('drawingCanvas');

    if (!desktopCanvas) {
      console.warn('⚠️ 找不到画布元素，跳过重绘');
      return;
    }

    console.log('✅ 找到画布元素:', desktopCanvas.width, 'x', desktopCanvas.height);

    // 🔥 关键修复：确保画布上下文正确初始化
    if (!this.ctx) {
      console.log('🔧 画布上下文未初始化，重新获取...');
      this.ctx = desktopCanvas.getContext('2d');
      this.canvas = desktopCanvas;
    }

    // 绘制到桌面端画布
    if (this.ctx) {
      console.log(`🎨 开始重新绘制画布: ${this.canvasStrokes.length} 个笔画`);
      console.log('📐 画布尺寸:', this.ctx.canvas.width, 'x', this.ctx.canvas.height);

      this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
      console.log('🧹 画布已清空');

      if (this.canvasStrokes.length > 0) {
        console.log('🖌️ 开始绘制笔画...');
        this.drawStrokesToCanvas(this.ctx);
        console.log('✅ 笔画绘制完成');
      } else {
        console.log('📭 没有笔画需要绘制');
      }
    } else {
      console.error('❌ 无法获取画布上下文');
    }
  }

  /**
   * 将笔画绘制到指定画布 - 重构版本，完美处理擦除模式
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  drawStrokesToCanvas(ctx) {
    console.log(`🖌️ drawStrokesToCanvas 开始，笔画数量: ${this.canvasStrokes.length}`);

    // 保存当前画布状态
    ctx.save();

    // 启用抗锯齿和平滑处理
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    let drawnCount = 0;
    let eraseCount = 0;

    // 按绘制顺序重放所有笔画
    this.canvasStrokes.forEach((stroke, index) => {
      if (!stroke.points || stroke.points.length < 1) return;

      // 为每个笔画保存状态
      ctx.save();

      // 🎯 关键：根据笔画类型精确设置绘制模式
      if (stroke.type === 'erase') {
        // 擦除模式：使用destination-out合成模式
        ctx.globalCompositeOperation = 'destination-out';
        ctx.strokeStyle = 'rgba(0,0,0,1)'; // 擦除时颜色不重要
        ctx.lineWidth = stroke.width || this.eraseSize;
        eraseCount++;
      } else {
        // 绘制模式：正常绘制
        ctx.globalCompositeOperation = 'source-over';
        ctx.strokeStyle = stroke.color || this.canvasSettings.strokeColor;
        const lineWidth = stroke.width || this.canvasSettings.strokeWidth;
        ctx.lineWidth = Math.max(1.5, lineWidth);
        drawnCount++;
      }

      // 统一的线条样式设置
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.miterLimit = 10;

      // 🚀 智能绘制：根据点数选择最佳绘制方式
      if (stroke.points.length === 1) {
        // 单点：绘制圆点
        const point = stroke.points[0];
        ctx.beginPath();
        ctx.arc(point.x, point.y, ctx.lineWidth / 2, 0, 2 * Math.PI);
        ctx.fill();
      } else if (stroke.points.length === 2) {
        // 两点：直线
        ctx.beginPath();
        ctx.moveTo(stroke.points[0].x, stroke.points[0].y);
        ctx.lineTo(stroke.points[1].x, stroke.points[1].y);
        ctx.stroke();
      } else {
        // 多点：平滑曲线（仅对绘制模式使用，擦除模式用直线确保效果）
        ctx.beginPath();
        ctx.moveTo(stroke.points[0].x, stroke.points[0].y);

        if (stroke.type === 'erase') {
          // 擦除模式：使用直线连接确保擦除效果
          for (let i = 1; i < stroke.points.length; i++) {
            ctx.lineTo(stroke.points[i].x, stroke.points[i].y);
          }
        } else {
          // 绘制模式：使用贝塞尔曲线平滑
          for (let i = 1; i < stroke.points.length - 1; i++) {
            const current = stroke.points[i];
            const next = stroke.points[i + 1];
            const cpx = (current.x + next.x) / 2;
            const cpy = (current.y + next.y) / 2;
            ctx.quadraticCurveTo(current.x, current.y, cpx, cpy);
          }

          // 处理最后一个点
          if (stroke.points.length > 1) {
            const last = stroke.points[stroke.points.length - 1];
            ctx.lineTo(last.x, last.y);
          }
        }

        ctx.stroke();
      }

      // 恢复每个笔画的状态
      ctx.restore();
    });

    // 恢复画布状态
    ctx.restore();

    // 重置绘制模式
    ctx.globalCompositeOperation = 'source-over';

    console.log(`✅ drawStrokesToCanvas 完成，绘制: ${drawnCount} 个，擦除: ${eraseCount} 个`);
  }

  /**
   * 开始绘制
   * @param {Event} event - 鼠标事件
   */
  startDrawing(event) {
    // 阻止右键菜单和默认行为
    event.preventDefault();

    // 记录鼠标按键信息
    console.log('鼠标按键事件:', {
      button: event.button,
      buttons: event.buttons,
      pointerType: event.pointerType || 'mouse',
    });

    this.drawing = true;
    // 重置绘制点，确保每次开始绘制都是新的平滑路径
    this.lastPoint = null;
    this.lastDrawPoint = null;
    // 只处理左键绘制
    if (event.button === 0) {
      // 设置桌面端画布
      if (this.ctx) {
        if (this.isEraseMode) {
          // 擦除模式设置
          this.ctx.globalCompositeOperation = 'destination-out';
          this.ctx.lineWidth = this.eraseSize;
        } else {
          // 绘制模式设置
          this.ctx.strokeStyle = this.canvasSettings.strokeColor;
          this.ctx.lineWidth = Math.max(1.5, this.canvasSettings.strokeWidth);
          this.ctx.globalCompositeOperation = 'source-over';
        }
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        this.ctx.miterLimit = 10;
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
      }

      // 移动端不需要画布功能，已移除相关代码

      // 开始新的笔画
      const currentStroke = {
        type: this.isEraseMode ? 'erase' : 'draw',
        color: this.isEraseMode ? 'rgba(0,0,0,1)' : this.canvasSettings.strokeColor,
        width: this.isEraseMode ? this.eraseSize : this.canvasSettings.strokeWidth,
        points: [],
        startTime: Date.now(),
      };

      // 获取起始坐标
      let x, y;
      if (event.offsetX !== undefined) {
        x = event.offsetX;
        y = event.offsetY;
      } else if (event.clientX !== undefined) {
        const rect = event.target.getBoundingClientRect();
        x = event.clientX - rect.left;
        y = event.clientY - rect.top;
      }

      if (x !== undefined && y !== undefined) {
        currentStroke.points.push({ x, y, time: Date.now() });
        this.canvasStrokes.push(currentStroke);
      }

      this.draw(event);
    }
  }

  /**
   * 停止绘制
   */
  async stopDrawing() {
    this.drawing = false;

    // 重置绘制点，确保下次绘制不会连接到这次的结束点
    this.lastPoint = null;
    this.lastDrawPoint = null;

    // 结束桌面端画布绘制
    if (this.ctx) {
      this.ctx.beginPath();
    }

    // 移动端不需要画布功能，已移除相关代码

    // 结束当前笔画
    if (this.canvasStrokes.length > 0) {
      const currentStroke = this.canvasStrokes[this.canvasStrokes.length - 1];
      currentStroke.endTime = Date.now();
      currentStroke.duration = currentStroke.endTime - currentStroke.startTime;
    }

    // 每次绘制结束时保存（使用防抖）
    await this.saveCanvasData();
  }

  /**
   * 绘制 - 使用平滑算法减少锯齿
   * @param {Event} event - 鼠标事件
   */
  draw(event) {
    if (!this.drawing) return;

    // 阻止页面滚动
    event.preventDefault();

    // 获取坐标，兼容不同事件类型
    let x, y;
    if (event.offsetX !== undefined) {
      x = event.offsetX;
      y = event.offsetY;
    } else if (event.clientX !== undefined) {
      const rect = event.target.getBoundingClientRect();
      x = event.clientX - rect.left;
      y = event.clientY - rect.top;
    }

    if (x !== undefined && y !== undefined) {
      // 获取平滑后的坐标
      const smoothPoint = this.getSmoothPoint(x, y);

      // 记录原始路径点（用于保存）
      if (this.canvasStrokes.length > 0) {
        const currentStroke = this.canvasStrokes[this.canvasStrokes.length - 1];
        currentStroke.points.push({ x, y, time: Date.now() });
      }

      // 使用平滑坐标绘制到桌面端画布
      if (this.ctx) {
        this.drawSmoothLine(this.ctx, smoothPoint.x, smoothPoint.y);
      }

      // 移动端不需要画布功能，已移除相关代码
    }
  }

  /**
   * 更新鼠标位置
   * @param {Event} event - 鼠标事件
   */
  updateMousePosition(event) {
    // 兼容不同事件类型
    if (event.offsetX !== undefined) {
      this.mouseX = event.offsetX;
      this.mouseY = event.offsetY;
    } else if (event.clientX !== undefined) {
      const rect = event.target.getBoundingClientRect();
      this.mouseX = event.clientX - rect.left;
      this.mouseY = event.clientY - rect.top;
    }
  }

  /**
   * 处理鼠标移动
   * @param {Event} event - 鼠标事件
   */
  handleMouseMove(event) {
    // 阻止页面滚动
    event.preventDefault();

    this.updateMousePosition(event);

    if (this.drawing) {
      this.draw(event);
    }

    // 始终显示指示器
    this.drawEraseIndicator();
  }

  /**
   * 处理鼠标离开
   */
  handleMouseLeave() {
    this.stopDrawing();
  }

  /**
   * 绘制擦除范围指示器 - 桌面端专用
   */
  drawEraseIndicator() {
    const indicatorCanvas = document.getElementById('indicatorCanvas');

    if (!this.showEraseIndicator) return;

    // 绘制桌面端指示器
    if (indicatorCanvas) {
      const indicatorCtx = indicatorCanvas.getContext('2d');
      indicatorCtx.clearRect(0, 0, indicatorCanvas.width, indicatorCanvas.height);
      indicatorCtx.strokeStyle = '#ff0000';
      indicatorCtx.lineWidth = 2;
      indicatorCtx.setLineDash([5, 5]);
      indicatorCtx.beginPath();
      indicatorCtx.arc(this.mouseX, this.mouseY, this.eraseSize / 2, 0, 2 * Math.PI);
      indicatorCtx.stroke();
    }
  }

  /**
   * 开始触摸绘制
   * @param {Event} event - 触摸事件
   */
  async startDrawingTouch(event) {
    // 强制阻止默认行为
    event.preventDefault();
    event.stopPropagation();

    // 检查是否已经有指针事件在处理（避免重复）
    if (event.touches.length === 1 && event.targetTouches.length === 1) {
      const touch = event.touches[0];
      const offsetX = touch.clientX - event.target.getBoundingClientRect().left;
      const offsetY = touch.clientY - event.target.getBoundingClientRect().top;

      // 记录触摸事件信息
      console.log('触摸事件:', {
        touches: event.touches.length,
        targetTouches: event.targetTouches.length,
        changedTouches: event.changedTouches.length,
        pressure: touch.force || '不支持',
        radiusX: touch.radiusX || '不支持',
        radiusY: touch.radiusY || '不支持',
        rotationAngle: touch.rotationAngle || '不支持',
      });

      // 只有在没有指针事件时才处理触摸事件
      if (!this.drawing) {
        // 模拟左键绘制
        this.drawing = true;
        this.ctx.strokeStyle = 'blue';
        this.ctx.lineWidth = Math.max(1.5, this.canvasSettings.strokeWidth);
        this.ctx.lineCap = 'round';
        this.ctx.lineTo(offsetX, offsetY);
        this.ctx.stroke();
        this.ctx.beginPath();
        this.ctx.moveTo(offsetX, offsetY);

        // 开始新的笔画并保存
        const currentStroke = {
          type: 'draw',
          color: 'blue',
          width: 1,
          points: [{ x: offsetX, y: offsetY, time: Date.now() }],
          startTime: Date.now(),
        };
        this.canvasStrokes.push(currentStroke);
      }
    }

    return false; // 确保阻止默认行为
  }

  /**
   * 触摸绘制
   * @param {Event} event - 触摸事件
   */
  async drawTouch(event) {
    // 强制阻止默认行为
    event.preventDefault();
    event.stopPropagation();

    const touch = event.touches[0];
    const offsetX = touch.clientX - event.target.getBoundingClientRect().left;
    const offsetY = touch.clientY - event.target.getBoundingClientRect().top;

    if (this.drawing) {
      // 记录路径点
      if (this.canvasStrokes.length > 0) {
        const currentStroke = this.canvasStrokes[this.canvasStrokes.length - 1];
        currentStroke.points.push({ x: offsetX, y: offsetY, time: Date.now() });
      }

      this.ctx.lineTo(offsetX, offsetY);
      this.ctx.stroke();
      this.ctx.beginPath();
      this.ctx.moveTo(offsetX, offsetY);
    }

    return false; // 确保阻止默认行为
  }

  /**
   * 指针事件处理函数 - 专门处理数位板笔
   * @param {Event} event - 指针事件
   */
  async handlePointerDown(event) {
    // 强制阻止默认行为
    event.preventDefault();
    event.stopPropagation();

    // 如果是笔，使用专门的笔处理逻辑
    if (event.pointerType === 'pen') {
      this.drawing = true;

      // 根据当前模式设置绘制参数
      if (this.isEraseMode) {
        // 擦除模式
        if (this.ctx) {
          this.ctx.globalCompositeOperation = 'destination-out';
          this.ctx.lineWidth = this.eraseSize;
        }

        // 移动端不需要画布功能，已移除相关代码
      } else {
        // 绘制模式
        if (this.ctx) {
          this.ctx.strokeStyle = 'blue';
          this.ctx.globalCompositeOperation = 'source-over';
          // 根据压力值调整线条粗细，使用更好的最小值减少锯齿
          this.ctx.lineWidth = Math.max(1.5, event.pressure * 5);
        }

        // 移动端不需要画布功能，已移除相关代码
      }

      // 更新笔的状态
      this.penPressure = event.pressure;
      this.penTiltX = event.tiltX;
      this.penTiltY = event.tiltY;

      // 开始新的笔画
      const currentStroke = {
        type: this.isEraseMode ? 'erase' : 'draw',
        color: this.isEraseMode ? 'transparent' : 'blue',
        width: this.isEraseMode ? this.eraseSize : Math.max(1, event.pressure * 5),
        points: [],
        startTime: Date.now(),
      };

      // 获取起始坐标
      let x, y;
      if (event.offsetX !== undefined) {
        x = event.offsetX;
        y = event.offsetY;
      } else if (event.clientX !== undefined) {
        const rect = event.target.getBoundingClientRect();
        x = event.clientX - rect.left;
        y = event.clientY - rect.top;
      }

      if (x !== undefined && y !== undefined) {
        currentStroke.points.push({ x, y, time: Date.now() });
        this.canvasStrokes.push(currentStroke);
      }

      this.draw(event);
    }

    return false; // 确保阻止默认行为
  }

  /**
   * 指针移动事件
   * @param {Event} event - 指针事件
   */
  async handlePointerMove(event) {
    // 强制阻止页面滚动
    event.preventDefault();
    event.stopPropagation();

    this.updateMousePosition(event);

    if (this.drawing && event.pointerType === 'pen') {
      // 根据当前模式设置绘制参数
      if (this.isEraseMode) {
        // 擦除模式
        if (this.ctx) {
          this.ctx.globalCompositeOperation = 'destination-out';
          this.ctx.lineWidth = this.eraseSize;
        }

        // 移动端不需要画布功能，已移除相关代码
      } else {
        // 绘制模式
        if (this.ctx) {
          this.ctx.globalCompositeOperation = 'source-over';
          this.ctx.strokeStyle = 'blue';
          // 根据压力值动态调整线条粗细，使用更好的最小值减少锯齿
          this.ctx.lineWidth = Math.max(1.5, event.pressure * 5);
        }

        // 移动端不需要画布功能，已移除相关代码
      }

      // 更新笔的状态
      this.penPressure = event.pressure;
      this.penTiltX = event.tiltX;
      this.penTiltY = event.tiltY;

      // 记录路径点
      if (this.canvasStrokes.length > 0) {
        const currentStroke = this.canvasStrokes[this.canvasStrokes.length - 1];
        let x, y;
        if (event.offsetX !== undefined) {
          x = event.offsetX;
          y = event.offsetY;
        } else if (event.clientX !== undefined) {
          const rect = event.target.getBoundingClientRect();
          x = event.clientX - rect.left;
          y = event.clientY - rect.top;
        }
        if (x !== undefined && y !== undefined) {
          currentStroke.points.push({ x, y, time: Date.now() });
        }
      }

      this.draw(event);
    }

    // 始终显示指示器
    this.drawEraseIndicator();

    return false; // 确保阻止默认行为
  }

  /**
   * 指针抬起事件
   * @param {Event} event - 指针事件
   */
  async handlePointerUp(event) {
    event.preventDefault();

    console.log('指针抬起事件:', {
      pointerId: event.pointerId,
      pointerType: event.pointerType,
      pressure: event.pressure,
    });

    if (event.pointerType === 'pen') {
      this.drawing = false;
      // 🔥 修复：不要重置擦除模式！保持用户选择的模式
      // this.isEraseMode = false; // 这行代码导致擦除模式被意外重置

      // 结束桌面端画布绘制
      if (this.ctx) {
        this.ctx.beginPath();
      }

      // 移动端不需要画布功能，已移除相关代码

      // 结束当前笔画并保存
      if (this.canvasStrokes.length > 0) {
        const currentStroke = this.canvasStrokes[this.canvasStrokes.length - 1];
        currentStroke.endTime = Date.now();
        currentStroke.duration = currentStroke.endTime - currentStroke.startTime;
      }

      await this.saveCanvasData();
    }
  }

  /**
   * 指针离开事件
   * @param {Event} event - 指针事件
   */
  async handlePointerLeave(event) {
    event.preventDefault();

    if (event.pointerType === 'pen') {
      this.drawing = false;

      // 结束桌面端画布绘制
      if (this.ctx) {
        this.ctx.beginPath();
      }

      // 移动端不需要画布功能，已移除相关代码

      // 结束当前笔画并保存
      if (this.canvasStrokes.length > 0) {
        const currentStroke = this.canvasStrokes[this.canvasStrokes.length - 1];
        currentStroke.endTime = Date.now();
        currentStroke.duration = currentStroke.endTime - currentStroke.startTime;
      }

      await this.saveCanvasData();
    }
  }

  /**
   * 切换绘制/擦除模式 - 增强版本，解决数位笔响应问题
   */
  async toggleDrawMode() {
    // 防抖机制：防止快速重复点击
    const now = Date.now();
    if (this.lastToggleTime && now - this.lastToggleTime < 300) {
      console.log('⚠️ 模式切换过于频繁，忽略此次操作');
      return;
    }
    this.lastToggleTime = now;

    console.log(
      `🔄 模式切换: ${this.isEraseMode ? '擦除' : '绘制'} -> ${!this.isEraseMode ? '擦除' : '绘制'}`,
    );

    // 切换模式状态
    this.isEraseMode = !this.isEraseMode;

    // 重置绘制状态，避免模式切换时的连线问题
    this.lastPoint = null;
    this.lastDrawPoint = null;
    this.drawing = false;

    // 清除任何正在进行的绘制操作
    if (this.ctx) {
      this.ctx.beginPath();
    }

    // 🎯 设置桌面端画布的模式
    const canvas = document.getElementById('drawingCanvas');
    const ctx = this.ctx;

    if (canvas && ctx) {
      // 清除当前路径
      ctx.beginPath();

      if (this.isEraseMode) {
        // 🚀 擦除模式配置
        ctx.globalCompositeOperation = 'destination-out';
        ctx.lineWidth = this.eraseSize;
        ctx.strokeStyle = 'rgba(0,0,0,1)'; // 擦除时颜色不重要

        // 擦除模式的特殊设置
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
      } else {
        // 🎨 绘制模式配置
        ctx.globalCompositeOperation = 'source-over';
        ctx.strokeStyle = this.canvasSettings.strokeColor;
        ctx.lineWidth = Math.max(1.5, this.canvasSettings.strokeWidth);

        // 绘制模式的高质量设置
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.miterLimit = 10;
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
      }
    }

    console.log(`✅ 模式切换完成: ${this.isEraseMode ? '擦除模式' : '绘制模式'}`);
    console.log(
      `📏 当前设置: 擦除大小=${this.eraseSize}, 绘制线宽=${this.canvasSettings.strokeWidth}`,
    );

    // 🔥 修复：不要在模式切换时保存数据，避免触发画布清空
    // await this.saveCanvasData(true);
    console.log('💡 模式切换完成，跳过数据保存以避免画布清空');
  }

  /**
   * 设置当前题目ID
   * @param {string} id - 题目ID
   */
  setCurrentId(id) {
    this.currentId = id;
  }

  // 🚀 ===== 实时传输相关方法 =====

  /**
   * 🚀 触发实时传输更新
   * @param {Array} newStrokes - 新增的笔画数据
   */
  triggerRealtimeUpdate(newStrokes) {
    try {
      // 检查是否有新笔画需要传输
      if (!newStrokes || newStrokes.length === 0) {
        return;
      }

      // 防止重复触发 - 检查是否正在传输中
      if (this._isTransmitting) {
        console.log('⚠️ 实时传输正在进行中，跳过此次触发');
        return;
      }

      // 设置传输标志
      this._isTransmitting = true;

      // 通过事件系统通知Store发送实时更新
      if (typeof window !== 'undefined' && window.canvasRealtimeCallback) {
        console.log(`🚀 触发实时传输: ${newStrokes.length} 个笔画`);
        window.canvasRealtimeCallback(newStrokes);
      }

      // 延迟重置传输标志，防止过于频繁的传输
      setTimeout(() => {
        this._isTransmitting = false;
      }, 1000); // 1秒防抖
    } catch (error) {
      console.error('触发实时传输失败:', error);
      this._isTransmitting = false; // 确保错误时也重置标志
    }
  }

  /**
   * 🎨 添加远程笔画到本地画布
   * @param {Object} remoteStroke - 远程笔画数据
   */
  addRemoteStroke(remoteStroke) {
    try {
      console.log('🎨 添加远程笔画:', remoteStroke);

      // 解压缩远程笔画数据
      const decompressedStroke = this.decompressRemoteStroke(remoteStroke);

      // 添加到本地笔画数组
      this.canvasStrokes.push(decompressedStroke);

      // 重新渲染画布
      this.redrawCanvas();
    } catch (error) {
      console.error('添加远程笔画失败:', error);
    }
  }

  /**
   * 🔄 从远程数据加载完整画布
   * @param {Object} remoteCanvasData - 远程画布数据
   */
  loadCanvasDataFromRemote(remoteCanvasData) {
    try {
      console.log('🔄 从远程加载画布数据:', remoteCanvasData);

      // 检查数据结构 - 支持多种格式
      const strokesData = remoteCanvasData?.s || remoteCanvasData?.strokes;
      if (!remoteCanvasData || !strokesData || strokesData.length === 0) {
        console.warn('远程画布数据为空或无笔画数据');
        return;
      }

      // 清空当前画布
      this.canvasStrokes = [];

      // 解压缩并添加所有笔画
      strokesData.forEach((compressedStroke) => {
        const decompressedStroke = this.decompressRemoteStroke(compressedStroke);
        this.canvasStrokes.push(decompressedStroke);
      });

      // 恢复设置
      if (remoteCanvasData.es) {
        this.eraseSize = remoteCanvasData.es;
      }

      // 重新渲染画布
      this.redrawCanvas();

      console.log(`✅ 成功加载 ${this.canvasStrokes.length} 个远程笔画`);
    } catch (error) {
      console.error('从远程加载画布数据失败:', error);
    }
  }

  /**
   * 🗜️ 解压缩远程笔画数据
   * @param {Object} compressedStroke - 压缩的笔画数据
   * @returns {Object} 解压缩后的笔画数据
   */
  decompressRemoteStroke(compressedStroke) {
    const version = compressedStroke.v || 4; // 默认v4格式

    if (version >= 4) {
      // v4.0格式解压缩
      return {
        type: compressedStroke.t === 1 ? 'erase' : 'draw',
        color: compressedStroke.c || this.canvasSettings.strokeColor,
        width: compressedStroke.w || this.canvasSettings.strokeWidth,
        points: (compressedStroke.p || []).map((pointData, index) => {
          const point = {
            x: pointData[0] || 0,
            y: pointData[1] || 0,
            time: compressedStroke.ts + index || Date.now(),
          };

          // 恢复数位笔高级属性
          if (pointData.length > 2) {
            point.pressure = pointData[2] || 1.0;
          }
          if (pointData.length > 4) {
            point.tiltX = pointData[3] || 0;
            point.tiltY = pointData[4] || 0;
          }
          if (pointData.length > 5) {
            point.twist = pointData[5] || 0;
          }

          return point;
        }),
        startTime: compressedStroke.ts || Date.now(),
        endTime: compressedStroke.ts || Date.now(),
        duration: 0,
        isRemote: true, // 标记为远程笔画
      };
    } else {
      // 兼容旧格式
      return {
        type: compressedStroke.t === 1 ? 'erase' : 'draw',
        color: compressedStroke.c || this.canvasSettings.strokeColor,
        width: compressedStroke.w || this.canvasSettings.strokeWidth,
        points: (compressedStroke.p || []).map((point) => ({
          x: point[0] || 0,
          y: point[1] || 0,
          time: Date.now(),
        })),
        startTime: Date.now(),
        endTime: Date.now(),
        duration: 0,
        isRemote: true,
      };
    }
  }

  /**
   * 🎨 重新渲染整个画布
   */
  redrawCanvas() {
    if (!this.ctx || !this.canvas) {
      return;
    }

    try {
      // 清空画布
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      // 重新绘制所有笔画
      this.canvasStrokes.forEach((stroke) => {
        this.drawStroke(stroke);
      });
    } catch (error) {
      console.error('重新渲染画布失败:', error);
    }
  }

  /**
   * 🖌️ 绘制单个笔画
   * @param {Object} stroke - 笔画数据
   */
  drawStroke(stroke) {
    if (!this.ctx || !stroke.points || stroke.points.length === 0) {
      return;
    }

    try {
      this.ctx.save();

      // 设置绘制样式
      if (stroke.type === 'erase') {
        this.ctx.globalCompositeOperation = 'destination-out';
        this.ctx.lineWidth = stroke.width || this.eraseSize;
      } else {
        this.ctx.globalCompositeOperation = 'source-over';
        this.ctx.strokeStyle = stroke.color || this.canvasSettings.strokeColor;
        this.ctx.lineWidth = stroke.width || this.canvasSettings.strokeWidth;
      }

      this.ctx.lineCap = 'round';
      this.ctx.lineJoin = 'round';

      // 绘制路径
      this.ctx.beginPath();

      if (stroke.points.length === 1) {
        // 单点绘制
        const point = stroke.points[0];
        this.ctx.arc(point.x, point.y, (stroke.width || 2) / 2, 0, 2 * Math.PI);
        this.ctx.fill();
      } else {
        // 多点路径绘制
        this.ctx.moveTo(stroke.points[0].x, stroke.points[0].y);

        for (let i = 1; i < stroke.points.length; i++) {
          const point = stroke.points[i];

          // 支持压感变化（如果有的话）
          if (point.pressure && stroke.type !== 'erase') {
            this.ctx.lineWidth = (stroke.width || 2) * point.pressure;
          }

          this.ctx.lineTo(point.x, point.y);
        }

        this.ctx.stroke();
      }

      this.ctx.restore();
    } catch (error) {
      console.error('绘制笔画失败:', error);
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 清理保存定时器
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }
  }

  /**
   * 获取画布状态 - 增强版本，包含调试信息
   */
  getCanvasState() {
    const state = {
      drawing: this.drawing,
      canvasStrokes: this.canvasStrokes,
      canvasSettings: this.canvasSettings,
      eraseSize: this.eraseSize,
      showEraseIndicator: this.showEraseIndicator,
      mouseX: this.mouseX,
      mouseY: this.mouseY,
      penPressure: this.penPressure,
      penTiltX: this.penTiltX,
      penTiltY: this.penTiltY,
      penSupported: this.penSupported,
      isEraseMode: this.isEraseMode,
      currentId: this.currentId,
      // 🎯 新增调试信息
      strokeCount: this.canvasStrokes.length,
      eraseStrokeCount: this.canvasStrokes.filter((s) => s.type === 'erase').length,
      drawStrokeCount: this.canvasStrokes.filter((s) => s.type !== 'erase').length,
      lastDrawPoint: this.lastDrawPoint,
      lastPoint: this.lastPoint,
    };

    return state;
  }

  /**
   * 调试方法：打印画布状态
   */
  debugCanvasState() {
    const state = this.getCanvasState();
    console.group('🎨 画布状态调试信息');
    console.log('📊 基本状态:', {
      isEraseMode: state.isEraseMode,
      drawing: state.drawing,
      currentId: state.currentId,
    });
    console.log('📏 尺寸设置:', {
      eraseSize: state.eraseSize,
      strokeWidth: state.canvasSettings.strokeWidth,
      strokeColor: state.canvasSettings.strokeColor,
    });
    console.log('📈 笔画统计:', {
      总笔画: state.strokeCount,
      绘制笔画: state.drawStrokeCount,
      擦除笔画: state.eraseStrokeCount,
    });
    console.log('🖱️ 鼠标状态:', {
      mouseX: state.mouseX,
      mouseY: state.mouseY,
      lastDrawPoint: state.lastDrawPoint,
    });
    console.groupEnd();
  }

  /**
   * 设置画布状态
   * @param {Object} state - 画布状态
   */
  setCanvasState(state) {
    if (state.drawing !== undefined) this.drawing = state.drawing;
    if (state.canvasStrokes !== undefined) this.canvasStrokes = state.canvasStrokes;
    if (state.canvasSettings !== undefined) this.canvasSettings = state.canvasSettings;
    if (state.eraseSize !== undefined) this.eraseSize = state.eraseSize;
    if (state.showEraseIndicator !== undefined) this.showEraseIndicator = state.showEraseIndicator;
    if (state.mouseX !== undefined) this.mouseX = state.mouseX;
    if (state.mouseY !== undefined) this.mouseY = state.mouseY;
    if (state.penPressure !== undefined) this.penPressure = state.penPressure;
    if (state.penTiltX !== undefined) this.penTiltX = state.penTiltX;
    if (state.penTiltY !== undefined) this.penTiltY = state.penTiltY;
    if (state.penSupported !== undefined) this.penSupported = state.penSupported;
    if (state.isEraseMode !== undefined) this.isEraseMode = state.isEraseMode;
    if (state.currentId !== undefined) this.currentId = state.currentId;
  }
}

// 创建默认实例
export const canvasManager = new CanvasManager();

// 导出工具函数
export const canvasUtils = {
  /**
   * 创建新的画布管理器实例
   */
  createManager() {
    return new CanvasManager();
  },

  /**
   * 获取默认画布管理器实例
   */
  getDefaultManager() {
    return canvasManager;
  },

  /**
   * 初始化画布（使用默认实例）
   */
  initCanvas(canvasId, indicatorCanvasId, containerSelector) {
    return canvasManager.initCanvas(canvasId, indicatorCanvasId, containerSelector);
  },

  /**
   * 清除画布（使用默认实例）
   */
  clearCanvas() {
    return canvasManager.clearCanvas();
  },

  /**
   * 保存画布数据（使用默认实例）
   */
  saveCanvasData(immediate = false) {
    return canvasManager.saveCanvasData(immediate);
  },

  /**
   * 加载画布数据（使用默认实例）
   */
  loadCanvasData() {
    return canvasManager.loadCanvasData();
  },

  /**
   * 切换绘制模式（使用默认实例）
   */
  toggleDrawMode() {
    return canvasManager.toggleDrawMode();
  },
};
