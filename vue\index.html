<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <style>
      html, body { touch-action: pan-x pan-y; }
    </style>
    

  <script>
  window.MathJax = {
    tex: {
      inlineMath: [['$', '$'], ['\\(', '\\)']],
      displayMath: [['$$', '$$'], ['\\[', '\\]']],
      processEscapes: true,
      // 1. 确保加载所有需要的包，特别是 ams (for boxed)、color 和 cancel
      packages: {'[+]': ['ams', 'color', 'cancel', 'boldsymbol', 'textmacros']},
      // 🔧 修复：确保单个 $$ 公式显示为内联模式
      tags: 'ams'
    },
    // 🔧 添加 CHTML 配置以确保正确的显示模式
    chtml: {
      displayAlign: 'left',
      displayIndent: '0em'
    },
    // 🔧 关键修复：配置MathJax处理所有元素，包括pre和code
    options: {
      skipHtmlTags: ['script', 'noscript', 'style', 'textarea'], // 移除了'pre'和'code'
      ignoreHtmlClass: 'tex2jax_ignore',
      processHtmlClass: 'tex2jax_process'
    },
    // 切换到 SVG 输出模式，以获得更好的兼容性和渲染效果
    svg: {
      fontCache: 'global'
    },
    startup: {
      ready: () => {
        console.log('🧮 MathJax is ready with SVG output! (支持pre/code块)');
        MathJax.startup.defaultReady();
      }
    }
  };
</script>
<!-- 最终解决方案：使用 tex-svg-full.js "完全体" 版本 -->
<!-- 它预先打包了所有常用扩展（包括 color），彻底解决了扩展包加载失败的问题 -->
<script src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg-full.js" async id="MathJax-script"></script>
    <!-- 引入霞鹜文楷字体（保持原字体配置） -->
    <link href="https://cdn.jsdelivr.net/npm/lxgw-wenkai-webfont@1.6.0/style.min.css" rel="stylesheet">
    
    <!-- 公式字体兼容设置 -->
    <style>
      @font-face {
        font-family: 'SimSunFallback';
        src: local('SimSun'), local('宋体'), local('Noto Serif SC'), local('Microsoft YaHei');
      }
      /* 确保 MathJax 公式使用指定字体 */
      mjx-container {
        font-family: 'SimSunFallback', 'STIXGeneral', serif !important;
      }
    </style>
    
    <title>Vite App</title>
  </head>
  <body>
    <div id="app"></div>
    <!-- Vue 应用入口（保持不变） -->
    <script type="module" src="/src/main.js"></script>
  </body>
</html>