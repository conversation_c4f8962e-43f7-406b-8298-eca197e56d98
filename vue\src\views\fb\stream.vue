<script setup>
  import { ref, computed, onBeforeUnmount, nextTick } from 'vue';
  import MarkdownIt from 'markdown-it'; // 导入 markdown-it
  import DOMPurify from 'dompurify';

  // —— 1. 初始化 markdown-it 实例，并启用 breaks 选项以模拟 marked 的行为 ——
  const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true, // 关键：将 \n 转换成 <br>
  });

  /** —— 流式显示时的内容 —— **/
  const output = ref('');
  /** —— 存储后端推过来的全部文本 —— **/
  const storedContent = ref('');
  /** —— 是否正在流式打印 —— **/
  const isStreaming = ref(false);
  /** —— SSE 连接对象 —— **/
  let eventSource = null;
  /** —— 临时缓冲队列 —— **/
  let bufferQueue = '';
  let typingInterval = null;
  let eventClosed = false;

  // “循环模式”开关
  const isLooping = ref(false);
  // SSE 结束后显示最终区
  const showFinal = ref(false);

  /** —— 打字效果 —— **/
  const startTyping = () => {
    if (!typingInterval) {
      typingInterval = setInterval(() => {
        if (bufferQueue.length > 0) {
          output.value += bufferQueue[0];
          bufferQueue = bufferQueue.slice(1);

          nextTick(() => {
            const div = document.getElementById('streamDiv');
            if (div) div.scrollTop = div.scrollHeight;
          });
        }
        if (bufferQueue.length === 0 && eventClosed) {
          clearInterval(typingInterval);
          typingInterval = null;
        }
      }, 20);
    }
  };

  /** —— 流式区的 HTML —— **/
  const htmlOutput = computed(() => {
    // markdown-it 已经配置了 breaks: true, 无需手动替换 \n
    const rawHtml = md.render(output.value);
    return DOMPurify.sanitize(rawHtml);
  });

  /** —— 最终区的 HTML —— **/
  const finalHtml = computed(() => {
    // markdown-it 已经配置了 breaks: true, 无需手动替换 \n
    const rawHtml = md.render(storedContent.value);
    return DOMPurify.sanitize(rawHtml);
  });

  /** —— 发起一次 SSE 的函数 —— **/
  const startSSE = async (loop = false) => {
    if (isStreaming.value) return;

    isStreaming.value = true;
    showFinal.value = false;
    output.value = '';
    storedContent.value = '';
    bufferQueue = '';
    eventClosed = false;

    eventSource = new EventSource('http://127.0.0.1:7001/updatefbsyzc?per=1&type=656598&model=2');

    eventSource.onmessage = (event) => {
      bufferQueue += event.data;
      storedContent.value += event.data;
      startTyping();
    };

    eventSource.addEventListener('end', () => {
      eventClosed = true;
      isStreaming.value = false;
      eventSource.close();
      showFinal.value = true;

      if (isLooping.value) {
        setTimeout(() => {
          startSSE();
        }, 500);
      }
    });

    eventSource.onerror = (err) => {
      console.error('SSE 错误', err);
      eventClosed = true;
      isStreaming.value = false;
      eventSource.close();
      showFinal.value = true;
      // 如果出错时也想“循环”，可以在这里加：
      // if (isLooping.value) { startStream() }
    };
  };

  const onClickStart = () => {
    startSSE();
  };
  const onClickLoop = () => {
    isLooping.value = !isLooping.value;
  };

  onBeforeUnmount(() => {
    if (typingInterval) clearInterval(typingInterval);
    if (eventSource) eventSource.close();
  });
</script>

<template>
  <a-row :gutter="[0, 16]">
    <!-- 左侧占位 -->
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left" />
    </a-col>

    <!-- 中间 -->
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center">
      <!-- 按钮区 -->
      <div style="display: flex; justify-content: center; margin-bottom: 1rem">
        <button
          :disabled="isStreaming"
          style="margin-right: 1rem; padding: 0.5rem 1rem; font-size: 14px"
          @click="onClickStart"
        >
          {{ isStreaming ? '正在加载…' : '开始' }}
        </button>

        <button
          :style="{
            padding: '0.5rem 1rem',
            fontSize: '14px',
            backgroundColor: isLooping ? '#52c41a' : '#f5f5f5',
            color: isLooping ? '#fff' : '#000',
            border: '1px solid #d9d9d9',
            cursor: 'pointer',
          }"
          @click="onClickLoop"
        >
          {{ isLooping ? '循环：已开启' : '循环：关闭' }}
        </button>
      </div>

      <!-- 实时流式打字区 -->
      <div v-if="isStreaming" id="streamDiv" class="stream-content">
        <div class="text-wrap" v-html="htmlOutput"></div>
      </div>

      <!-- SSE 完成后整体渲染一次 -->
      <div v-if="showFinal" class="stream-content" style="margin-top: 1rem; background-color: #fff">
        <div class="text-wrap" v-html="finalHtml"></div>
      </div>
    </a-col>

    <!-- 右侧占位 -->
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right" />
    </a-col>
  </a-row>
</template>

<style scoped>
  .stream-content {
    background-color: #f5f5f5;
    padding: 1rem;
    height: 800px; /* 根据需要可自行调整 */
    overflow-y: auto;
    border-radius: 8px;
    font-family: monospace;
    font-size: 14px;
  }

  /* 保持换行并允许软换行 */
  .text-wrap {
    white-space: pre-wrap;
    word-break: break-word;
    position: relative;
  }

  /* 打字光标动画 */
  .text-wrap::after {
    content: '';
    display: inline-block;
    width: 8px;
    height: 1em;
    background: black;
    margin-left: 2px;
    animation: blink 1s steps(2, start) infinite;
    vertical-align: bottom;
  }

  @keyframes blink {
    to {
      visibility: hidden;
    }
  }

  /* 高亮 markdown 常用元素 */
  .text-wrap h1,
  .text-wrap h2,
  .text-wrap h3 {
    margin: 0.5em 0;
  }
  .text-wrap code {
    background: #eee;
    padding: 0.2em 0.4em;
    border-radius: 4px;
  }
  .text-wrap pre {
    background: #333;
    color: #fff;
    padding: 1em;
    border-radius: 6px;
    overflow-x: auto;
  }
</style>
