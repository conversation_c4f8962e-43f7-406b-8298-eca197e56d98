module.exports = (app) => {
  // 应用启动完成
  app.ready(async () => {
    try {
      // 检查是否为生产环境
      const isProduction = app.config.env === 'prod';

      if (isProduction) {
        // 获取系统信息
        const os = require('os');
        const packageInfo = require('./package.json');

        // 生产环境启动通知
        const startupMessage = `🎉 Egg应用启动成功！
🌟 环境: ${app.config.env}
🚀 启动时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
📦 应用版本: ${packageInfo.version || '未知'}
🔧 Node版本: ${process.version}
💻 进程ID: ${process.pid}
🖥️ 系统: ${os.type()} ${os.release()}
💾 内存: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB
📍 运行目录: ${process.cwd()}
🌐 监听端口: ${process.env.PORT || '7001'}
⚡ CPU核心: ${os.cpus().length}个
✨ 所有服务已就绪，系统运行正常！`;

        // 发送启动成功通知到飞书
        const ctx = app.createAnonymousContext();
        await ctx.service.feishu['fs'](startupMessage);
        console.log('✅ 启动成功通知已发送到飞书');
      } else {
        // 开发环境只在控制台输出
        console.log('🎉 Egg应用启动成功 (开发环境)');
      }
    } catch (error) {
      console.error('❌ 发送启动通知失败:', error.message);
    }
  });

  // 格式化运行时长为年月日小时分秒
  function formatUptime(seconds) {
    // 使用更精确的时间计算
    const YEAR_SECONDS = 365.25 * 24 * 3600; // 考虑闰年
    const MONTH_SECONDS = 30.44 * 24 * 3600; // 平均月份天数
    const DAY_SECONDS = 24 * 3600;
    const HOUR_SECONDS = 3600;
    const MINUTE_SECONDS = 60;

    let remaining = seconds;

    const years = Math.floor(remaining / YEAR_SECONDS);
    remaining = remaining % YEAR_SECONDS;

    const months = Math.floor(remaining / MONTH_SECONDS);
    remaining = remaining % MONTH_SECONDS;

    const days = Math.floor(remaining / DAY_SECONDS);
    remaining = remaining % DAY_SECONDS;

    const hours = Math.floor(remaining / HOUR_SECONDS);
    remaining = remaining % HOUR_SECONDS;

    const minutes = Math.floor(remaining / MINUTE_SECONDS);
    const secs = Math.floor(remaining % MINUTE_SECONDS);

    const parts = [];
    if (years > 0) parts.push(`${years}年`);
    if (months > 0) parts.push(`${months}月`);
    if (days > 0) parts.push(`${days}日`);
    if (hours > 0) parts.push(`${hours}小时`);
    if (minutes > 0) parts.push(`${minutes}分钟`);
    if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`);

    return parts.join('');
  }

  // 应用关闭前的回调
  app.beforeClose(async () => {
    try {
      // 释放 redis 锁
      await app.redis.set('zhihuwenda', '2');
      console.log('✅ Redis锁 zhihuwenda 已被释放');

      const isProduction = app.config.env === 'prod';

      if (isProduction) {
        const uptimeSeconds = process.uptime();
        const formattedUptime = formatUptime(uptimeSeconds);

        const shutdownMessage = `🛑 Egg应用正在关闭
⏰ 关闭时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
💻 进程ID: ${process.pid}
📊 运行时长: ${formattedUptime}
👋 应用即将停止服务`;

        const ctx = app.createAnonymousContext();
        await ctx.service.feishu['fs'](shutdownMessage);
        console.log('✅ 关闭通知已发送到飞书');
      }
    } catch (error) {
      console.error('❌ 发送关闭通知或释放Redis锁失败:', error.message);
    }
  });
};
