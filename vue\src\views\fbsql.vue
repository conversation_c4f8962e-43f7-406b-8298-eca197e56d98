<script>
  export default {
    name: 'Xrsql',
    data: function () {
      return {
        yy: 0,
        shu: 0,
        tuili: 0,
        zhiliao: 0,
        cate: [],
      };
    },
    mounted() {
      setInterval(() => {
        this.getData();
      }, 500);
    },
    methods: {
      getData() {
        axios.get('/egg/fbsql').then((res) => {
          // this.yy = res.data.yy[0].yytotal;
          // this.shu = res.data.shu[0].shutotal;
          // this.tuili = res.data.tuili[0].tuilitotal;
          // this.zhiliao = res.data.zhiliao[0].zhiliaototal;
          this.cate = res.data;
          console.log(this.cate);
        });
      },
    },
  };
</script>

<template>
  <a-row v-for="item in cate" :gutter="24">
    <a-col :span="24" style="background-color: #ececec; padding: 20px">
      <a-card :title="item.name" :bordered="false">
        <p>{{ item.totalsql }}</p>
      </a-card>
    </a-col>
  </a-row>
</template>

<style scoped></style>
