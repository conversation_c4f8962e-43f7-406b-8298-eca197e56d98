<template>
  <el-alert :title="timer" type="success" center> </el-alert>
  <el-table
    :data="tableData"
    style="width: 100%"
    :row-class-name="tableRowClassName"
    border
    :default-sort="{ prop: 'total', order: 'descending' }"
  >
    <el-table-column fixed prop="dw" label="单位" sortable :width="getScreen()"> </el-table-column>
    <el-table-column prop="bms" label="报名数" sortable width="80"> </el-table-column>
    <el-table-column prop="shtg" label="审核通过" sortable width="80"> </el-table-column>
    <el-table-column prop="wsh" label="未审核" sortable width="80"> </el-table-column>
    <el-table-column prop="zks" label="招考数" sortable width="80"> </el-table-column>
    <el-table-column prop="zw" label="职位" sortable width="260"> </el-table-column>
    <el-table-column prop="kslx" label="考试类型" sortable width="180"> </el-table-column>
  </el-table>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  const tableData = ref([]);
  const timer = ref('');
  const route = useRoute();
  const tableRowClassName = ({ row, rowIndex }) => {
    if (
      row.dw.match('00202') ||
      row.dw.match('00210') ||
      row.dw.match('14026') ||
      row.dw.match('14081') ||
      row.dw.match('14208') ||
      row.dw.match('14214') ||
      row.dw.match('14215') ||
      row.dw.match('15058') ||
      row.dw.match('16090') ||
      row.dw.match('16095') ||
      row.dw.match('18030') ||
      row.dw.match('18064') ||
      row.dw.match('18201') ||
      row.dw.match('19055') ||
      row.dw.match('19084') ||
      row.dw.match('19090') ||
      row.dw.match('20095') ||
      row.dw.match('21158') ||
      row.dw.match('21204') ||
      row.dw.match('22026') ||
      row.dw.match('22223') ||
      row.dw.match('24026') ||
      row.dw.match('24058') ||
      row.dw.match('24081') ||
      row.dw.match('26184') ||
      row.dw.match('27054') ||
      row.dw.match('28132') ||
      row.dw.match('30025') ||
      row.dw.match('31055') ||
      row.dw.match('31132') ||
      row.dw.match('31228')
    ) {
      return 'success-row';
    } else if (
      row.dw.match('00180') ||
      row.dw.match('00203') ||
      row.dw.match('21134') ||
      row.dw.match('25216') ||
      row.dw.match('26221') ||
      row.dw.match('30210') ||
      row.dw.match('32002') ||
      row.dw.match('32204') ||
      row.dw.match('32208')
    ) {
      return 'warning-row';
    } else if (
      row.dw.match('22201') ||
      row.dw.match('24210') ||
      row.dw.match('25212') ||
      row.dw.match('26221') ||
      row.dw.match('27204') ||
      row.dw.match('27205') ||
      row.dw.match('27207') ||
      row.dw.match('27208') ||
      row.dw.match('27210') ||
      row.dw.match('27211') ||
      row.dw.match('27212') ||
      row.dw.match('27213') ||
      row.dw.match('27215') ||
      row.dw.match('28141') ||
      row.dw.match('28203') ||
      row.dw.match('28207') ||
      row.dw.match('29201') ||
      row.dw.match('29206') ||
      row.dw.match('32206')
    ) {
      return 'man-row';
    }
    return '';
  };

  const getData = () => {
    const years = route.query.years || '2025';
    let params = {
      years: years,
    };
    axios.get('/egg/gwyrs', { params }).then((res) => {
      tableData.value = res.data.list;
      timer.value = res.data.timer;
    });
  };

  const getScreen = () => {
    if (document.body.clientWidth < 1000) {
      return 180;
    } else {
      return 260;
    }
  };

  onMounted(() => {
    getData();
    getScreen();
  });
</script>

<style>
  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .el-table .man-row {
    background: #e5f2ff;
  }
</style>
