<script>
  export default {
    name: 'Xrsql',
    data: function () {
      return {
        total: 0,
        total1: 0,
        reurlisnull: 0,
        lock: [],
        finish: [],
        progress: 0,
      };
    },
    mounted() {
      setInterval(() => {
        this.getData();
      }, 500);
      setInterval(() => {
        this.getData1();
      }, 500);
    },
    methods: {
      getData() {
        axios.get('/egghk/xrsql').then((res) => {
          this.total = res.data.total[0].total;
          this.reurlisnull = res.data.reurlisnull[0].reurlisnull;
          this.finish = this.total - this.reurlisnull;
          //取小数点后两位
          this.progress = ((this.finish / this.total) * 100).toFixed(2);
          this.lock = res.data.lock;
        });
      },
      getData1() {
        axios.get('/egg/dstotal').then((res) => {
          this.total1 = res.data[0].total;
        });
      },
    },
  };
</script>

<template>
  <div style="background-color: #ececec; padding: 20px">
    <a-row :gutter="16">
      <a-col :span="8">
        <a-card title="总数" :bordered="false">
          <p>{{ total }}</p>
        </a-card>
        <a-card title="ds总数" :bordered="false">
          <p>{{ total1 }}</p>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="空数" :bordered="false">
          <p>{{ reurlisnull }}</p>
          <p>{{ finish }}</p>
          <a-progress :percent="progress" status="active" />
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="lock数" :bordered="false">
          <p v-for="lockx in lock" :key="lock.id">
            {{ lockx.lock }}
          </p>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped></style>
