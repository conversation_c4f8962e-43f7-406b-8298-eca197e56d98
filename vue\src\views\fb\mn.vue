<template>
  <a-row>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="lb" style="" @click="left"></div>
    </a-col>
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center">
      <div v-show="showTop">
        <span class="top-input" style="position: fixed; top: 0; z-index: 99">
          <a-input-number id="inputNumber" v-model:value="perpage" :min="1" @change="getData" />
          <a-button @click="prev_page">p</a-button>
          <a-input-number
            id="inputNumber"
            v-model:value="current_page"
            :min="1"
            @change="getData"
          />
          <a-button @click="next_page">n</a-button>
          <a-button @click="allshow">all</a-button>
          <a-button @click="getData">刷新</a-button>

          <a-button @click="toggleBackground">背景</a-button>
          <a-button @click="copyText">复制</a-button>
          <a-button @click="copyText(2)">复制2</a-button>
          <div
            v-show="showCanvas"
            style="
              background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
              position: fixed;
              top: 0;
              position: relative;
            "
          >
            <div class="top-button">
              <a-button @click="toggleCanvas">画布1</a-button>
              <a-button @click="initCanvas">init画布</a-button>
            </div>
          </div>
        </span>
      </div>
      <div class="wp">
        <canvas
          v-show="showCanvas"
          id="drawingCanvas"
          style="position: absolute; background-color: rgba(255, 255, 255, 0)"
          @mousedown="startDrawing"
          @mouseup="stopDrawing"
          @mousemove="draw"
          @mouseleave="stopDrawing"
          @touchstart="startDrawingTouch"
          @touchmove="drawTouch"
          @touchend="stopDrawing"
        ></canvas>
        <div v-html="zltimu.content"></div>

        <a-row>
          <a-col v-for="(item, index) in data" :key="index" :span="24" class="datalist">
            <div v-if="isLoading">Loading...</div>
            <div v-if="!isLoading" :class="index === 1 ? 'items' : ''">
              <div
                v-if="(index > 0 && data[index - 1].name !== data[index].name) || index === 0"
                class="kjname"
              >
                <p>================================</p>
                <p id="kjname">{{ data[index].name }}</p>
              </div>

              <div v-if="showtimu" id="maintimu" class="maintimu" v-html="item.content"></div>

              <div>
                <div v-if="item.xuanxiang && +item.type !== 204" class="item">
                  <p v-html="item.xuanxiang"></p>
                </div>
                <div v-else class="item">
                  <p class="an_a" v-html="item.A"></p>
                  <p class="an_b" v-html="item.B"></p>
                  <p class="an_c" v-html="item.C"></p>
                  <p class="an_d" v-html="item.D"></p>
                </div>
              </div>

              <!--              <p-->
              <!--                @click="toggleTop"-->
              <!--                v-if="item.type === 205 && (index + 1) % 5 !== 0"-->
              <!--              >-->
              <!--                <br />-->
              <!--              </p>-->
              <p v-if="item.type === 205 && (index + 1) % 5 === 0" @click="toggleTop">
                ================================
              </p>
              <p v-else-if="item.type !== 205" @click="toggleTop">================</p>
              <div v-if="item.ds" v-show="showds">
                <div style="color: red" @click="open_comment(item.id)">
                  <span style="color: blue">{{ item.choice }}</span>
                  {{ item.source }}{{ item.createdTime }}
                  <span style="color: blue"
                    >{{ '正确率：' + Math.round(item.correctRatio) }}{{ '易错项：'
                    }}{{
                      +item.mostWrongAnswer === 0
                        ? 'A'
                        : +item.mostWrongAnswer === 1
                          ? 'B'
                          : +item.mostWrongAnswer === 2
                            ? 'C'
                            : +item.mostWrongAnswer === 3
                              ? 'D'
                              : item.mostWrongAnswer
                    }}</span
                  >
                  <p style="color: purple">{{ item.cate ? item.cate : '' }}</p>
                </div>
                <br />
                <Shuxue :content="item.ds" />
                <p>================</p>
              </div>
              <div v-show="showContent" class="answer">
                <div
                  v-if="ansinfo"
                  class="ansinfo"
                  @click="+perpage !== 1 ? open_comment(item.oid) : (showcomment = true)"
                >
                  {{ item.source }}{{ item.createdTime }}
                </div>
                <br />
                <div v-if="daanblock">
                  <div :style="showdaanblock" v-html="item.solution"></div>
                  <div>
                    <dp1 v-if="showvideo" :url="item.oid" :uid="item.oid"></dp1>
                  </div>
                </div>
                <div></div>
                <p>==================</p>
              </div>
              <div>
                <comment v-if="showcomment && +perpage === 1" :uid="+item.oid"></comment>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
      <div v-show="showTop" class="top">
        <span class="top-input">
          <!--          <a-input-number-->
          <!--            id="inputNumber"-->
          <!--            v-model:value="perpage"-->
          <!--            @change="getData"-->
          <!--            :min="1"-->
          <!--          />-->
          <!--          <a-button @click="prev_page">p</a-button>-->
          <!--          <a-input-number-->
          <!--            id="inputNumber"-->
          <!--            v-model:value="current_page"-->
          <!--            @change="getData"-->
          <!--            :min="1"-->
          <!--          />-->
          <!--          <a-button @click="next_page">n</a-button>-->
          <a-button @click="toggledaanblock">隐藏答案块</a-button>
          <a-button @click="toggleshowdaan">
            {{ showdaan === true ? '已' : '不' }}显示答案{{ total }}
          </a-button>
          <!--          <a-button @click="recoverpage">恢复页数</a-button>-->
          <a-button class="showans" @click="togglean">显示答案</a-button>
          <a-button @click="toggleansblock">隐藏答案</a-button>
          <a-button @click="toggleTop">隐藏顶部内容</a-button>
          <!--          <fbhiddentag></fbhiddentag>-->
          <a-button @click="toggleCanvas">画布</a-button>
          <a-button @click="initCanvas">init画布</a-button>
          <!-- <a-button @click="savepage">保存页数</a-button> -->
          <a-button @click="showvideo = !showvideo">{{
            showvideo === true ? '隐藏视频' : '显示视频'
          }}</a-button>
          <a-button @click="zuotivideo = !zuotivideo">{{
            zuotivideo === false ? '开视频' : '关视频'
          }}</a-button>
        </span>
        <div class="top-button"></div>
      </div>
    </a-col>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="rb" style="" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';
  import { message } from 'ant-design-vue';
  import DPlayer from 'dplayer';
  import dp from '../../components/dp.vue';
  import { useStore } from '@/store/temp';
  import Shuxue from '@/components/shuxue.vue';

  const zltimu = ref([]);
  const data = ref([]);
  const isLoading = ref(true);
  const showTop = ref(true);
  const current_page = ref(1);
  const showContent = ref(false);
  const perpage = ref(1);
  const total = ref(0);
  const route = useRoute();
  const isTagHidden = ref(false);
  const showdaan = ref(false);
  const showdaanblock = ref('');
  const resolution = ref('');
  const open_count = ref(0);
  const drawing = ref(false);
  const showCanvas = ref(false);
  const ctx = ref(null);
  const daanblock = ref(true);
  const isBackgroundActive = ref(true);
  const showvideo = ref(false);
  const showtimu = ref(true);
  const zuotivideo = ref(false);
  const ansinfo = ref(true);
  const showcomment = ref(true);
  const showds = ref(true);
  const currenttype = ref(666);
  const store = useStore();
  const allshow = async () => {
    current_page.value = 1;
    perpage.value = 999999;
    await getData();
  };
  const open_comment = (id) => {
    window.open(
      '/fb/comment?id=' + id,
      '_blank',
      'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=800,height=600',
    );
  };
  const getResolution = () => {
    resolution.value = `${window.innerWidth} x ${window.innerHeight}`;
  };
  const toggleHiddenTag = () => {
    // 使用 document.querySelectorAll 获取所有具有 class="tag" 的元素
    const elements = document.querySelectorAll('.tag');

    // 使用 forEach 方法遍历元素，并根据状态变量切换它们的显示/隐藏状态
    elements.forEach((element) => {
      if (isTagHidden.value) {
        element.style.display = 'block'; // 或者其他适当的显示样式
      } else {
        element.style.display = 'none';
      }
    });

    // 切换状态变量
    isTagHidden.value = !isTagHidden.value;
  };

  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const toggledaanblock = () => {
    daanblock.value = !daanblock.value;
  };

  const prev_page = () => {
    left();
  };
  const next_page = () => {
    right();
  };

  const toggleshowdaan = async () => {
    showdaan.value = !showdaan.value;
    await initCanvas();
  };

  const toggleCanvas = async () => {
    showCanvas.value = !showCanvas.value;
  };
  const toggleansblock = async () => {
    showContent.value = !showContent.value;
    await ansblack();
  };
  const handleKeyDown = async (event) => {};

  const updateValue = async (delta) => {
    open_count.value = 0;
    const newValue = +current_page.value + delta;
    if (newValue >= 1 && newValue <= 100000) {
      current_page.value = newValue;
      console.log(current_page.value);
      await getData();
      if (!showdaan.value) {
        showContent.value = false;
        await ansblack();
      } else {
        showContent.value = true;
        await ansred();
      }
    }
  };

  const right = async () => {
    await updateValue(1);
  };

  const left = async () => {
    await updateValue(-1);
  };

  const toggleContent = async (event) => {
    if (event.key === 'ArrowUp' || event.key === 'ArrowDown' || event.key === 'w') {
      await getData();
      if (showContent.value) {
        await ansblack();
        showContent.value = false;
      } else {
        await ansred();
        const showAnsButton = document.querySelector('.showans');
        showAnsButton.click();
        showContent.value = true;
      }
    }
    if (event.key === 'ArrowLeft') {
      await left();
    } else if (event.key === 'ArrowRight') {
      await right();
    }
  };

  const ansred = async () => {
    await ansblack();
    for (let i = 0; i < data.value.length; i++) {
      // console.log(data.value);
      if (data.value && data.value[i].choice === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'red';
      }
      if (data.value && data.value[i].choice === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'red';
      }
      if (data.value && data.value[i].choice === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'red';
      }
      if (data.value && data.value[i].choice === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'red';
      }
    }
  };

  const ansblack = async () => {
    // console.log('ansblack', data.value);
    for (let i = 0; i < data.value.length; i++) {
      document.getElementsByClassName('an_a')[i].style.color = 'black';
      document.getElementsByClassName('an_b')[i].style.color = 'black';
      document.getElementsByClassName('an_c')[i].style.color = 'black';
      document.getElementsByClassName('an_d')[i].style.color = 'black';
    }
  };

  const togglean = async (answer) => {
    if (showContent.value && !showdaan.value && open_count.value === 0) {
      await ansblack();
      showContent.value = false;
    } else {
      open_count.value++;
      await ansred();
      showContent.value = true;
    }
    // console.log('打开次数', open_count.value);
  };
  const recoverpage = async () => {
    const url = '/egg/fbrecoverpage';
    const id = route.query.id || currenttype.value;
    const b = route.query.b || 0;
    const type = route.query.type || 'sy';
    if (b === 0) {
      let params = {
        id: +type,
        type: type,
      };
      console.log('recoverpage', id);
      const response = await axios.get(url, { params });
      current_page.value = response.data.page || current_page.value;
      console.log(current_page.value);
    }
    await getData();
    await ansblack();
  };
  const savepage = async () => {
    const url = '/egg/fbremeber';
    const type = route.query.type || '1';
    const id = route.query.id || currenttype.value;
    console.log(`savepage`, id);
    const b = route.query.b || 0;
    if (b === 0) {
      const response = await axios.post(url, {
        id: +type,
        type: type,
        page: current_page.value,
      });
      console.log(`savepage`, id);
      if (+response.data.affectedRows !== 0) {
        console.log(`记住${current_page.value}成功`);
        message.success({
          content: `记住${current_page.value}成功`,
          duration: 1,
          style: {
            marginTop: '80vh',
          },
        });
      }
    }
  };
  const insertdata = async (id, sort) => {
    const url = '/egg/fbupdatezlfx5000';
    let params = {
      id: id,
      sort: sort,
      biao: 'yanyu5000',
    };
    console.log(params);

    try {
      const response = await axios.get(url, { params });
      console.log(response);
      if (response.status === 200) {
        if (response.data.code === 0) {
          message.success({
            content: response.data.message,
            duration: 1,
            style: {
              marginTop: '80vh',
            },
          });
        } else if (response.data.code === 1) {
          message.error({
            content: response.data.message,
            duration: 1,
            style: {
              marginTop: '60vh',
            },
          });
        }
      }
    } catch (error) {
      message.error(error.response.data.message);
    }
  };
  const pushans = async (id, answer) => {
    const url = '/egg/fbchoice';
    const type = route.query.type || 'sy';
    console.log(type);
    const response = await axios.post(url, {
      id: id,
      choice: answer,
      type: type,
    });
    // console.log(response);
    if (response.data.affectedRows !== 0) {
      message.success({
        content: `选择${answer}成功`,
        style: {
          marginTop: '80vh',
        },
      });
      await getData();
      await ansred();
      await savepage();
      showContent.value = true;
    }
  };
  const initCanvas = async () => {
    const canvas = document.getElementById('drawingCanvas');
    canvas.width = document.querySelector('.wp').offsetWidth;
    canvas.height = document.querySelector('body').offsetHeight;
    ctx.value = canvas.getContext('2d');
  };

  const startDrawing = (event) => {
    drawing.value = true;
    draw(event);
  };

  const stopDrawing = () => {
    drawing.value = false;
    ctx.value.beginPath();
  };

  const draw = (event) => {
    if (!drawing.value) return;
    ctx.value.strokeStyle = 'blue';
    ctx.value.lineWidth = 3;
    ctx.value.lineCap = 'round';
    ctx.value.lineTo(event.offsetX, event.offsetY);
    ctx.value.stroke();
    ctx.value.beginPath();
    ctx.value.moveTo(event.offsetX, event.offsetY);
  };

  const startDrawingTouch = (event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const offsetX = touch.clientX - event.target.getBoundingClientRect().left;
    const offsetY = touch.clientY - event.target.getBoundingClientRect().top;
    startDrawing({
      offsetX,
      offsetY,
    });
  };

  const drawTouch = (event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const offsetX = touch.clientX - event.target.getBoundingClientRect().left;
    const offsetY = touch.clientY - event.target.getBoundingClientRect().top;
    draw({
      offsetX,
      offsetY,
    });
  };

  const toggleBackground = async () => {
    if (isBackgroundActive.value) {
      document.body.style.background = '#FAF5EB';
    } else {
      document.body.style.background = '';
    }
    isBackgroundActive.value = !isBackgroundActive.value;
  };
  const copyText = (type = 1) => {
    let datax = data.value[0];
    console.log(datax);

    // 将<p>和</p>替换为换行符\n，并删除所有其他的HTML标签
    let contentWithoutHtml = datax.content
      .replace(/<p>/gi, '') // 删除开头的<p>标签
      .replace(/<\/p>/gi, '\n') // 将结束的</p>标签替换为换行符
      .replace(/<[^>]*>/g, ''); // 删除所有其他HTML标签
    let solutionWithoutHtml = datax.solution
      .replace(/<p>/gi, '') // 删除开头的<p>标签
      .replace(/<\/p>/gi, '\n') // 将结束的</p>标签替换为换行符
      .replace(/<[^>]*>/g, ''); // 删除所有其他HTML标签
    let solution = '';
    solution = +type === 2 ? solutionWithoutHtml : '';
    // 构建最终的文本
    let text = `${contentWithoutHtml}${datax.A}\n${datax.B}\n${datax.C}\n${datax.D}\n====================================
      \n${solution}`;

    // 将文本复制到剪贴板
    navigator.clipboard.writeText(text);
  };
  const getData = async () => {
    perpage.value = +perpage.value;
    showvideo.value = false;

    window.scrollTo({ top: 0, behavior: 'smooth' });

    const type = route.query.type || '201,202,203,204,205,206,207,208';
    const per = route.query.per || perpage.value;
    const a = route.query.a || false;
    const id = route.query.id || 48905;
    const z = route.query.z || 1;
    const b = route.query.b || 0;
    const t = route.query.t || 0;
    const ljtk = route.query.ljtk || 0;
    const q = route.query.q || '1=1';
    const biao = route.query.biao || 'fbsykj';
    const page = route.query.page || current_page.value;
    currenttype.value = 666;
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/jskaojuanshumu';
    let params = {
      biao: biao,
      per: per,
      page: page,
      id: id,
      type: type,
      z: z,
      b: b,
      q: q,
      t: !t ? 0 : t,
    };
    // console.log(params);
    try {
      const response = await axios.get(url, { params });
      // if (response.data.length === 0) {
      //   console.log('z1', z);
      //   await getData();
      //   return;
      // }
      await savepage();
      if (a) {
        showContent.value = true;
      }
      // console.log(response.data.pagetotal[0].total);
      data.value = response.data;
      let i = 1;
      console.log('data.value.length', data.value.length);
      // if (per !== 1 && page !== 1) {
      //   i = (page - 1) * per + 1;
      // }
      window.document.title = data.value[0].oid;
      for (let item in data.value) {
        window.document.title = data.value[item].id;
        console.log('item', item);
        console.log('store.kjname', store.kjname);

        if (store.kjname !== data.value[item].name) {
          console.log('data.value[item].name', data.value[item].name);
          i = 1;
          store.setkjname(data.value[item].name);
        }
        if (item > 0) {
          if (data.value[item - 1].name !== data.value[item].name) {
            console.log('data.value[item - 1].name', data.value[item - 1].name);
            console.log('data.value[item].name', data.value[item].name);
            i = 1;
          }
        }
        if (data.value[item].solution) {
          data.value[item].solution = data.value[item].solution
            .replace(/fenbike/g, 'fbstatic')
            .replace(/<p>/g, '')
            .replace(/<\/p>/g, '');
        }
        if (data.value[item].A) {
          data.value[item].A =
            'A.' +
            data.value[item].A.replace(/fenbike/g, 'fbstatic')
              .replace(/<p>/g, '')
              .replace(/<\/p>/g, '');
        }
        if (data.value[item].B) {
          data.value[item].B =
            'B.' +
            data.value[item].B.replace(/fenbike/g, 'fbstatic')
              .replace(/<p>/g, '')
              .replace(/<\/p>/g, '');
        }
        if (data.value[item].C) {
          data.value[item].C =
            'C.' +
            data.value[item].C.replace(/fenbike/g, 'fbstatic')
              .replace(/<p>/g, '')
              .replace(/<\/p>/g, '');
        }
        if (data.value[item].D) {
          data.value[item].D =
            'D.' +
            data.value[item].D.replace(/fenbike/g, 'fbstatic')
              .replace(/<p>/g, '')
              .replace(/<\/p>/g, '');
        }

        if (per === 1) {
          i = page;
        }
        data.value[item].content = data.value[item].content
          .replace(/&nbsp;/g, '')
          .replace(/<p>\s*/g, '<p>')
          .replace(
            /<p>/,
            '<p><span style="color:red;">' +
              `(` +
              (+data.value[item].choice === 0
                ? 'A'
                : +data.value[item].choice === 1
                  ? 'B'
                  : +data.value[item].choice === 2
                    ? 'C'
                    : 'D') +
              `)` +
              '</span>.' +
              i +
              '.',
          )
          .replace(/fenbike/g, 'fbstatic')
          // 优化后的 input 标签匹配逻辑
          .replace(/<input\b[^>]*\bsize="(\d+)"[^>]*>/g, (match, size) => {
            return '_'.repeat(Number(size));
          });
        i++;

        if (1) {
          data.value[item].solution =
            data.value[item].solution.replace(
              /<p>/,
              '<p><span style="color:blue;">' +
                (+data.value[item].choice === 0
                  ? 'A'
                  : +data.value[item].choice === 1
                    ? 'B'
                    : +data.value[item].choice === 2
                      ? 'C'
                      : 'D') +
                '</span>.',
            ) ||
            data.value[item].solution.replace(
              /<p align="center">/,
              '<p><span style="color:blue;">' +
                (+data.value[item].choice === 0
                  ? 'A'
                  : +data.value[item].choice === 1
                    ? 'B'
                    : +data.value[item].choice === 2
                      ? 'C'
                      : 'D') +
                '</span>.',
            );
        }

        data.value[item].solution = data.value[item].solution.replace(/A项/g, '<br />A项');
        data.value[item].solution = data.value[item].solution.replace(/B项/g, '<br />B项');
        data.value[item].solution = data.value[item].solution.replace(/C项/g, '<br />C项');
        data.value[item].solution = data.value[item].solution.replace(/D项/g, '<br />D项');

        data.value[item].choice =
          +data.value[item].choice === 0
            ? 'A'
            : +data.value[item].choice === 1
              ? 'B'
              : +data.value[item].choice === 2
                ? 'C'
                : 'D';

        if (
          data.value[item].C &&
          (+data.value[item].C.length < 8 || data.value[item].C.match('image'))
        ) {
          data.value[item].xuanxiang =
            `<span class="an_a">${data.value[item].A}${'&nbsp;'.repeat(6)}</span><span class="an_b">${data.value[item].B}</span><span class="an_c">${'&nbsp;'.repeat(6)}${data.value[item].C}</span><span class="an_d">${'&nbsp;'.repeat(6)}${data.value[item].D}`;
        } else if (data.value[item].C && +data.value[item].C.length <= 16) {
          data.value[item].xuanxiang =
            `<p><span class="an_a">${data.value[item].A}</span>${'&nbsp;'.repeat(6)}<span class="an_b">${data.value[item].B}</span></p><p><span class="an_c">${data.value[item].C}</span>${'&nbsp;'.repeat(6)}<span class="an_d">${data.value[item].D}</span></p>`;
        } else {
          data.value[item].xuanxiang =
            `<p class="an_a">${data.value[item].A}</p><p class="an_b">${data.value[item].B}</p><p class="an_c">${data.value[item].C}</p><p class="an_d">${data.value[item].D}</p>`;
        }
        if (!data.value[item].C) {
          data.value[item].xuanxiang = '';
        }
      }
      if (+z && type === '201') {
        function formatString(input) {
          let formatted = '';
          for (let i = 0; i < input.length; i++) {
            formatted += input[i];
            if ((i + 1) % 5 === 0 && i + 1 !== input.length) {
              formatted += ' ';
            }
          }
          console.log(formatted);
          return formatted;
        }

        let anslist = [];
        let text = '';
        let i = 0; // Start from 0 to match array indexing
        let j = 0;

        for (let item in data.value) {
          text += data.value[item].choice;

          if ((i + 1) % 15 === 0) {
            const formattedString = formatString(text);
            anslist.push(formattedString);

            // console.log(j + 1);
            // console.log(formattedString);

            // data.value[j * 15].name += `<br>` + formattedString;

            text = '';
            j++;
          }
          i++;
        }
      }
      if (+ljtk === 1) {
        let x = [];
        console.log('ljtk');
        for (let item of data.value) {
          if ((+item.id - 21) % 130 >= 0 && (+item.id - 21) % 130 < 20) {
            console.log('ljtk');
            x.push(item);
          }
        }
        data.value = x;
      }
      if (+ljtk === 2) {
        let x = [];
        for (let item of data.value) {
          if ((+item.id - 41) % 130 >= 0 && (+item.id - 41) % 130 < 20) {
            console.log('ljtk');
            x.push(item);
          }
        }
        data.value = x;
      }
      if (+b === 1) {
        showtimu.value = false;
        console.log(showtimu.value);
      }
      if (q !== '1=1') {
        ansinfo.value = true;
      }
      if (+t === 1) {
        console.log(1, ansinfo.value);
        const res = await axios.get(`/egg/jscuo`);
        console.log(res.data);
        let x = [];
        for (let item of data.value) {
          for (let resitem of res.data) {
            if (+item.id === +resitem.sort) {
              x.push(item);
            }
          }
        }
        data.value = x;
      }
      // ansblack();
      // showContent.value = false;
      // 获取目标元素
      // const target = document.getElementById('kjname');
      // 平滑滚动至元素顶部
      // target.scrollIntoView({ behavior: 'smooth', block: 'start' });
      isLoading.value = false;
      await initCanvas();
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  onMounted(async () => {
    currenttype.value = 666;
    document.addEventListener('keydown', toggleContent);
    await toggleBackground();
    await recoverpage();
    window.addEventListener('keydown', handleKeyDown);
    getResolution();
    await initCanvas();
    window.addEventListener('resize', getResolution);
    window.addEventListener('resize', initCanvas);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
    window.removeEventListener('resize', getResolution);
    window.removeEventListener('resize', initCanvas);
  });
</script>

<style>
  /* body {
  background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
} */
  * {
    margin: 0;
    padding: 0;
    font-size: 20px;
    font-family: 'Microsoft YaHei', sans-serif, sans-serif;
  }

  button {
    line-height: 0 !important;
  }

  .top {
    background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
    position: fixed;
    bottom: 0;
  }

  img {
    max-width: 100%;
    max-height: 100%;
  }

  .top {
    display: flex;
    flex-direction: column;
  }

  .top-button,
  .top-input {
    display: flex;
    flex-wrap: wrap;

    gap: 10px; /* Adjust the gap as needed */
  }

  .wp {
    color: black;
    height: 100%;
    width: 100%; /* 宽度占满父容器 */
    max-width: 100vw; /* 最大宽度不超过视口宽度 */
    word-wrap: break-word; /* 在单词内部进行换行 */
    overflow-x: hidden;
    padding-top: 33px;
    padding-bottom: 62px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 1024px) {
    * {
      margin: 0;
      padding: 0;
      font-size: 24px;
    }
  }

  @media only screen and (max-width: 576px) {
    * {
      font-size: 20px;
    }

    :where(.ant-pagination .ant-pagination-options) {
      display: inline-block !important;
    }

    :where(.css-dev-only-do-not-override-hkh161).ant-pagination {
      width: 100%;
    }

    .rb {
      background-color: rgba(240, 240, 240, 0.3);
    }

    :global(.ant-pagination .ant-pagination-options) {
      display: inline-block !important;
    }

    .top-button {
      display: block;
    }

    .top-input {
      display: block;
    }

    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }

  canvas {
    border: 1px solid black;
  }

  #drawingCanvas {
    z-index: 10; /* 确保画布在内容之上 */
    max-width: 100vw; /* 确保画布宽度不超过视口宽度 */
  }
</style>
