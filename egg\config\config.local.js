/* eslint valid-jsdoc: "off" */

'use strict';

/**
 * @param {Egg.EggAppInfo} appInfo app info
 */

module.exports = (appInfo) => {
  /**
   * built-in config
   * @type {Egg.EggAppConfig}
   **/
  const config = (exports = {});
  exports.cors = {
    origin: '*',
    // 表示允许的源
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH',
    // 表示允许的http请求方式
  };
  exports.mysql = {
    // 单数据库信息配置
    client: {
      // host
      host: '************',
      // 端口号
      port: '11436',
      // 用户名
      user: 'root',
      // 密码
      password: 'wangcong',
      // 数据库名
      database: 'zz',
      charset: 'utf8mb4',
    },
    // 是否加载到 app 上，默认开启
    app: true,
    // 是否加载到 agent 上，默认关闭
    agent: false,
  };
  exports.redis = {
    client: {
      host: '************',
      port: 2639,
      password: '',
      db: '0',
    },
    agent: true,
  };
  exports.logger = {
    dir: '/logs/egg',
    level: 'INFO',
  };

  return {
    logger: {
      appLogName: `${appInfo.name}-web.log`,
      coreLogName: 'egg-web.log',
      agentLogName: 'egg-agent.log',
      errorLogName: 'common-error.log',
    },
    ...config,
  };
};
