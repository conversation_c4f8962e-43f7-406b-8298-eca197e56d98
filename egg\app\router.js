'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = (app) => {
  const { router, controller, io } = app;
  const auth = app.middleware.auth();
  router.get('/', controller.home['index']);
  router.get('/git', controller.home['gitpull']);
  router.get('/showip', controller.home['showip']);
  router.get('/news', controller.news['list']);
  router.get('/button', controller.home['button']);
  router.get('/test-socket', controller.home['testSocket']);

  // === 日志监控路由 ===
  router.get('/logs', auth, controller.log['index']);
  router.get('/logs/simple', auth, async (ctx) => {
    await ctx.render('log-simple.html');
  });
  router.get('/logs/test', auth, async (ctx) => {
    await ctx.render('log-test.html');
  });
  router.get('/logs/debug', auth, async (ctx) => {
    await ctx.render('eventsource-debug.html');
  });

  // 登录相关
  router.get('/login', async (ctx) => {
    await ctx.render('login.html');
  });
  router.post('/login', controller.login.login); // 兼容旧版登录页面
  router.post('/api/login', controller.login.login);
  router.get('/logout', controller.login.logout);

  // === 开关管理路由 ===
  router.get('/sw', auth, controller.sw.index);
  router.get('/api/sw/switches', auth, controller.sw.getSwitches);
  router.post('/api/sw/update', auth, controller.sw.updateSwitch);

  // === 定时任务管理路由 ===
  router.get('/dingshi', auth, controller.schedule.index);
  router.get('/api/schedule/list', auth, controller.schedule.getSchedules);
  router.post('/api/schedule/update', auth, controller.schedule.updateSchedule);
  router.post('/api/schedule/execute', auth, controller.schedule.executeSchedule);
  router.post('/api/sw/create', auth, controller.sw.createSwitch);
  router.delete('/api/sw/delete/:id', auth, controller.sw.deleteSwitch);
  router.get('/api/logs/web', auth, controller.log['webStream']);
  router.get('/api/logs/agent', auth, controller.log['agentStream']);
  router.get('/api/logs/error', auth, controller.log['errorStream']);
  router.get('/api/logs/download', auth, controller.log['download']);
  router.post('/api/logs/clear', auth, controller.log['clear']);
  router.get('/api/logs/info', controller.log['info']);

  // === 部署通知路由 ===
  router.post('/api/deploy/success', controller.deploy['success']);
  router.get('/api/deploy/success', controller.deploy['success']); // 支持GET测试
  router.post('/api/deploy/failure', controller.deploy['failure']);
  router.get('/api/deploy/failure', controller.deploy['failure']); // 支持GET测试
  router.get('/api/deploy/test', controller.deploy['test']);
  router.get('/api/deploy/template', controller.deploy['template']);
  // router.get('/xr', controller.pc['url']);
  // router.get('/xr/all', controller.pc['all']);
  // router.get('/xr/info', controller.pc['info']);
  // router.get('/xr/show', controller.pc['show']);
  // router.get('/xr/xrpage', controller.pc['xiurenp']);
  // router.get('/xr/nav', controller.pc['nav']);
  // router.get('/xr/xrinfo', controller.pc['xrinfo']);
  // router.get('/xr/xrfm', controller.pc['xrfm']);
  // router.get('/xr/sql', controller.pc['sqlinfo']);
  router.get('/bb', controller.pc['bb']);
  router.get('/zz', controller.pc['zz']);
  router.get('/zzz', controller.pc['zzz']);
  router.get('/test-webhook', controller.home['webhookTest']);

  router.get('/sydw', controller.pc['zzsydw']);
  router.get('/sydwsql', controller.pc['sydwsql']);
  router.get('/zwxq', controller.pc['zwxq']);
  router.get('/zw', controller.pc['zw']);
  router.get('/xm', controller.sydw['xm']);
  router.get('/xmsydw', controller.sydw['xmsydw']);
  router.get('/qzsydw', controller.sydw['qzsydw']);
  router.get('/qz', controller.sydw['qz']);
  // router.get('/sydw', controller.sydw['sydw']);
  router.get('/sydwweihu', controller.pc['zzsydwweihu']);
  router.get('/rkbm', controller.pc['ruankaobaoming']);
  router.get('/rkcj', controller.pc['rk']);
  router.get('/fjzz', controller.sydw['fjzzsydw']);
  router.get('/fjzz0429', controller.sydw['fjzz20230429']);
  router.get('/fjzz0826', controller.sydw['fjzz20230826']);
  router.get('/zzsydw', controller.sydw['fjzz']);
  router.get('/szsydw', controller.sydw['szsydw']);
  router.get('/fjzzdd', controller.sydw['fjzzdd']);
  router.get('/fjxmdd', controller.sydw['fjxmdd']);
  router.get('/xminfo', controller.sydw['xminfo']);
  router.get('/rsj', controller.sydw['rsj']);
  router.get('/gwy', controller.sydw['gwy']);
  router.get('/gwyrs', controller.sydw['gwysignupcount']);
  router.get('/chengji', controller.sydw['chengji']);
  router.get('/cj', controller.sydw['cj']);
  router.get('/ptgzrc', controller.sydw['ptgzrc']);
  router.get('/lysydw', controller.sydw['lysydw']);

  router.get('/xrnew/xrinfo', controller.xrnew['xrinfo']);
  router.get('/xrnew/show', controller.xrnew['show']);
  router.get('/xrnew/savexr', controller.xrnew['savexr']);
  router.get('/xr/:id', controller.xrnew['inshow']);
  router.get('/XiuRen/:id', controller.xrnew['inshow']);
  router.get('/xrdown', controller.xrnew['saveinxr']);
  router.get('/xrst', controller.xrnew['getinshowstatus']);
  router.get('/down/:id', controller.xrnew['down']);
  router.get('/save', controller.xrnew['saveorurl']);

  router.get('/tele/cong', controller.tele['cong']);
  router.get('/tele/dy', controller.tele['dy']);
  router.get('/tele/dy1', controller.tele['dy1']);
  router.post('/tele/dy3', controller.tele['dy3']);
  router.get('/tele/dy3', controller.tele['dy3']);
  router.get('/tele/dy4', controller.tele['dy4']);

  router.get('/tele/upload', controller.tele['upload']);
  router.post('/tele/upload', controller.tele['upload']);
  router.get('/tele/upload1', controller.tele['upload1']);
  router.get('/loc/id', controller.pc['locid']);
  router.get('/loc/checkid', controller.pc['checkid']);
  router.get('/tz', controller.tz['index']);

  router.get('/num', controller.pc['ltnum']);
  // router.get('/bs', controller.pc['bs']);
  // router.post('/bs', controller.pc['bs']);
  router.get('/virmach', controller.pc['virmach']);
  router.get('/vir', controller.pc['vir']);
  router.get('/redis', controller.pc['redis']);

  router.get('/st', controller.tz['getStat']);

  router.get('/tq', controller.pc['tq']);
  router.get('/hhhk', controller.pc['hhhk']);
  router.get('/gullochi', controller.pc['gullochi']);

  router.get('/ydhm/', controller.pc['ydhm']);
  router.get('/ydhm8/', controller.pc['ydhm8']);
  router.get('/ydhm8c/', controller.pc['ydhm8c']);
  router.get('/ydhm/:id', controller.pc['ydhm']);
  router.get('/ydhm4', controller.pc['ydnofour']);
  router.get('/ydhmall', controller.pc['ydfour']);

  router.get('/wxh', controller.pc['wxh']);
  router.get('/nfc', controller.pc['nfc']);

  router.get('/gwycount', controller.pc['gwy']);
  router.get('/zzrc', controller.pc['zzrc']);
  router.get('/zzrcsydw', controller.pc['zzrcsydw']);
  router.get('/zzsydw', controller.pc['zzsydw']);
  router.get('/zzrczkz', controller.pc['zzrczkz']);

  router.get('/dp', controller.pc['dp']);

  router.get('/lizhi', controller.pc['lizhi']);

  router.get('/min', controller.min['index']);
  router.get('/mingwy', controller.min['indexgwy']);
  router.get('/mintimu', controller.min['timu']);
  router.get('/minget', controller.min['get']);
  router.get('/mingetgj', controller.min['getgj']);
  router.get('/minnavjson', controller.min['navjson']);
  router.get('/minnav', controller.min['nav']);
  router.get('/minnav1', controller.min['nav1']);
  router.get('/mintree', controller.min['tree']);
  router.get('/cha', controller.min['cha']);
  router.get('/mincha', controller.min['mincha']);
  router.get('/tiku', controller.min['tiku']);
  router.get('/card', controller.min['card']);
  router.get('/cardgj', controller.min['cardgj']);
  router.post('/minupdate', controller.min['update']);
  router.post('/minupdatezhenti', controller.min['minupdatezhenti']);
  router.post('/mincuoti', controller.min['cuoti']);
  router.get('/cardall', controller.min['cardall']);
  router.get('/cardalljson', controller.min['cardalljson']);
  router.get('/updatesid', controller.min['updatesid']);
  router.post('/intimu', controller.min['intimu']);
  router.get('/intimu', controller.min['intimu']);
  router.get('/minan', controller.min['an']);
  router.post('/minchoice', controller.min['choice']);
  router.get('/minchoice', controller.min['choice']);
  router.get('/minrefinish', controller.min['refinish']);

  router.get('/alisms', controller.ali['main']);
  router.get('/pc', controller.pc['controlpc']);
  router.get('/pclist', controller.pc['pclist']);
  router.get('/lzlist', controller.pc['lizhilist']);
  router.get('/lzwin', controller.pc['lizhiwinlist']);

  router.get('/pong', controller.pc['pong']);
  router.get('/xccf', controller.pc['xcchufa']);
  router.get('/xchl', controller.pc['xchuilai']);
  router.get('/rejc', controller.pc['rejc']);
  router.get('/xchlall', controller.pc['xcallhuilai']);
  router.get('/xccfall', controller.pc['xcallchufa']);
  router.get('/xcall', controller.pc['xcallCommon']);
  router.get('/xcxm', controller.pc['xcallxm']);
  router.get('/xclz', controller.pc['xcalllz']);
  router.get('/hfxm', controller.pc['xmair']);
  router.get('/lzxm', controller.pc['xmairlz']);
  router.get('/xmlz', controller.pc['xmairxm']);
  router.get('/sda', controller.pc['sda']);
  router.get('/sdaxmlz', controller.pc['sdaxmlz']);
  router.get('/sdaxmch', controller.pc['sdaxmch']);
  router.get('/sdahfhl', controller.pc['sdahfhl']);
  router.get('/sdalzxm', controller.pc['sdalzxm']);
  router.get('/jsgg', controller.pc['jiaoshigonggao']);
  router.get('/tomato', controller.pc['tomato']);

  router.get('/lytest', controller.air['lytest']);
  router.get('/airall', controller.air['all']);

  router.get('/zyx', controller.sydw['zyx']);
  router.get('/zyxinfo', controller.sydw['zyxinfo']);

  router.get('/socretime', controller.sydw['socretime']);
  router.get('/ProjectByName', controller.sydw['ProjectByName']);

  router.get('/zzgxjyfz', controller.sydw['zzgxjyfz']);
  router.get('/gxjyfz', controller.sydw['gxjyfz']);

  router.get('/vue3', controller.pc['vue']);
  router.get('/pianyuan', controller.pc['pianyuan']);
  router.get('/speedtestcn', controller.pc['speedtestcn']);
  router.get('/apsgo', controller.pc['apsgo']);

  router.get('/fbcookie', controller.fb['setcookie']);
  router.post('/fbcookie', controller.fb['setcookie']);
  router.post('/fbcookie1', controller.fb['setcookie1']);
  router.get('/fbcate', controller.fb['cate']);
  router.get('/fbsycate', controller.fb['sycate']);
  router.get('/fbgettimu', controller.fb['gettimu']);
  router.get('/fbgethtimu', controller.fb['gethtimu']);
  router.get('/getjstimu', controller.fb['getjstimu']);
  router.get('/fbgettimu2', controller.fb['gettimu2']);
  router.get('/fbtimu', controller.fb['timu']);
  router.get('/fbrecate', controller.fb['recate']);
  router.get('/fballcate', controller.fb['allcate']);
  router.get('/fballcate1', controller.fb['allcate1']);
  router.get('/fbtree', controller.fb['tree']);
  router.get('/fbtreejs', controller.fb['treejs']);
  router.get('/fbtree1', controller.fb['tree1']);
  router.get('/fbsw', controller.fb['sw']);
  router.get('/fbchazlfx', controller.fb['chazlfx']);
  router.get('/fbcha', controller.fb['cha']);
  router.get('/fbupdatetag', controller.fb['updatetag']);
  router.get('/fbupdate5000', controller.fb['update5000']);
  router.get('/fbupdatezlfx5000', controller.fb['updatezlfx5000']);
  router.get('/fbzhiliao', controller.fb['zhiliao']);
  router.get('/fbzhiliaoall', controller.fb['zhiliaoall']);
  router.get('/fbchoice', controller.fb['choice']);
  router.post('/fbchoice', controller.fb['choice']);
  router.get('/fbchoice1', controller.fb['choice1']);
  router.get('/fbchengyu', controller.fb['chengyu1']);
  router.get('/fbchengyu2', controller.fb['chengyu2']);
  router.get('/fbchengyuall', controller.fb['chengyuall']);
  router.get('/fbchengyuupdate', controller.fb['chengyuupdate']);
  router.get('/fbcy', controller.fb['chengyu3']);
  router.get('/fbfix5000', controller.fb['fix5000']);
  router.get('/fbcopysql', controller.fb['copysql']);
  router.get('/fbfindp', controller.fb['findp']);
  router.get('/fbrecoverpage', controller.fb['recoverpage']);
  router.post('/fbremeber', controller.fb['remeberpage']);
  router.get('/fbyy', controller.fb['yyall']);
  router.get('/compleveltwo', controller.fb['compleveltwo']);
  router.get('/fbcatesy', controller.fb['catesy']);
  router.get('/fbrenewgen', controller.fb['renewgen']);
  router.get('/fbincr', controller.fb['incr']);
  router.post('/fbincr', controller.fb['incr']);
  router.get('/fbsubmit', controller.fb['submit']);
  router.post('/fbsubmit', controller.fb['submit']);
  router.get('/fbdown', controller.fb['down']);
  router.get('/yanyu5000', controller.fb['yanyu5000']);
  router.get('/fbhistory', controller.fb['addhistory']);
  router.get('/fbjskaojuan', controller.fb['getjskaojuan']);
  router.get('/fbxxkaojuan', controller.fb['getxxkaojuan']);
  router.get('/jskaojuanshumu', controller.fb['jskaojuanshumu']);
  router.get('/fbgetmnkaojuan', controller.fb['getmnkaojuan']);
  router.get('/fbgetskkaojuan', controller.fb['getskkaojuan']);
  router.get('/fbgetgkkaojuan', controller.fb['getgkkaojuan']);
  router.get('/jscuo', controller.fb['jscuo']);
  router.get('/downkjvedio', controller.fb['downkjvedio']);
  router.get('/fbgetvideo', controller.fb['getvideo']);
  router.get('/fbmncy', controller.fb['mncy']);
  router.get('/fbgetmncy', controller.fb['getmncy']);
  router.get('/fbgetmncyjs', controller.fb['getmncyjs']);
  router.get('/fbskcy', controller.fb['skcy']);
  router.get('/fbgetkj', controller.fb['getkj']);
  router.get('/fbupdatecorrectRatio', controller.fb['updatecorrectRatio']);
  router.get('/fbupdatecorrectRatio1', controller.fb['updatecorrectRatio1']);
  router.get('/fbgetcy', controller.fb['getcy']);
  router.get('/fbdown5000', controller.fb['down5000']);
  router.get('/fbfixtimu', controller.fb['fixtimu']);
  router.post('/fbfixtimu', controller.fb['fixtimu']);
  router.get('/fbpl', controller.fb['pinglun']);
  router.get('/fbupdate5000zlfx', controller.fb['update5000zlfx']);
  router.get('/fbq', controller.fb['getquestion']);
  router.get('/fbzlfx5000', controller.fb['zlfx5000']);
  router.get('/fbjszgms', controller.fb['jszgms']);
  router.get('/fbdownloadjsms', controller.fb['downloadjsms']);
  router.get('/fbjsms', controller.fb['fbjsms']);
  router.get('/chatimu', controller.fb['getchaxunquestion']);
  router.get('/chazlfxtimu', controller.fb['getchaxunzlfxquestion']);
  router.get('/uppic', controller.fb['uppic']);
  router.post('/uppic', controller.fb['uppic']);
  router.post('/pushtimu', controller.fb['pushtimu']);
  router.get('/downtimu', controller.fb['downtimu']);
  router.get('/downkjlistvideo', controller.fb['downkjlistvideo']);
  router.get('/downkjvedio1', controller.fb['downkjvedio1']);
  router.get('/dellastdata', controller.fb['dellastdata']);
  router.get('/getsykaojuan', controller.fb['getsykaojuan']);
  router.get('/updatetimu', controller.fb['updatetimu']);
  router.post('/updatetimu', controller.fb['updatetimu']);
  router.post('/getds', controller.fb['getds']);
  router.get('/getzlfx', controller.fb['getzlfx']);
  router.get('/gettimurank', controller.fb['gettimurank']);
  router.get('/upfbsykjallceteid', controller.fb['upfbsykjallceteid']);
  router.get('/updateidsnull', controller.fb['updateidsnull']);
  router.post('/canvas', controller.fb['canvas']);
  router.get('/canvas', controller.fb['canvas']);
  router.get('/get', controller.fb['canvas']);
  router.post('/thinkprocess/stop', controller.ai['stopthinkprocess']);

  router.get('/setrkxjcookie', controller.fb['setrkxjcookie']);
  router.post('/setrkxjcookie', controller.fb['setrkxjcookie']);
  router.get('/rkxj', controller.fb['rkxj']);
  router.get('/clashsw', controller.home['clashsw']);
  router.get(
    '/MSXzanaUZdwJUZqNbNwrDgVgVivDHNjyVotopTTcSRDGgkEDxDnSHkLZJkPX',
    controller.home['clashsw1'],
  );
  router.get('/shabi', controller.home['showclashconf']);

  router.get('/fbjzkj', controller.fb['jzkj']);
  router.get('/fbgetjzkaojuan', controller.fb['getjzkaojuan']);

  router.get('/saveipranges', controller.pc['saveip_rangestomysql']);
  router.get('/checkip', controller.pc['checkdnswebsites']);
  router.get('/cnwebsites', controller.pc['cnwebsites']);
  router.get('/singboxsite', controller.pc['singboxsite']);
  router.get('/bilibili', controller.pc['bilibili']);
  router.get('/chinaip', controller.pc['chinaip']);
  router.get('/querylog', controller.pc['querylog']);
  router.get('/shadowrule', controller.pc['shadowrule']);
  router.get('/zpgq', controller.pc['zpgq']);
  router.get('/check0928', controller.sydw['check0928']);
  router.get('/jszp', controller.sydw['jszp']);

  router.get('/up', (ctx) => {
    ctx.redirect('http://************:1286/');
  });

  require('./routes/stats')(app);
  require('./routes/tx')(app);
  require('./routes/xr')(app);
  require('./routes/feishu')(app);
  require('./routes/wecom')(app);
  require('./routes/dd')(app);
  require('./routes/ai')(app);

  // 通知相关路由
  router.post('/api/notification/alert', controller.notification.alert);
  router.get('/api/notification/test', controller.notification.test);
  router.get('/api/notification/health-report', controller.notification.healthReport);

  // GitHub Webhook 自动部署
  router.post('/webhook/github', controller.home.githubWebhook);
  router.get('/webhook/github', controller.home['webhookTest']); // 测试路由
  router.get('/api/deployment/status', controller.home.deploymentStatus);

  // 加载socket.io路由
  require('./socket/thinkprocess')(app);
  require('./socket/test')(app);
  require('./socket/canvas')(app); // 🎨 画布实时传输路由
};
