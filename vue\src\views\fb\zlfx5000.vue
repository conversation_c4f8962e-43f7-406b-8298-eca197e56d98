<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div v-show="showTop" class="top">
          <a-button @click="increaseFontSize">增大字体</a-button>
          <a-button @click="togglean(data.referenceAnswer)">显示答案</a-button>
          <a-button>{{ total }}</a-button>
          <a-button @click="toggleTop">隐藏顶部内容</a-button>
          <a-button @click="anstype = !anstype">答案模式</a-button>
          <a-button @click="showvideo = !showvideo">{{
            showvideo === true ? '隐藏视频' : '显示视频'
          }}</a-button>
          <a-input v-model:value="biaoming" placeholder="表名"></a-input>
          <el-pagination
            v-model:current-page="current1"
            v-model:page-size="pageSize3"
            show-quick-jumper
            :page-sizes="[1, 2, 3, 4, 6, 8, 20, 10, 50, 62, 100, 200, 300, 400, 2000]"
            layout="sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="getData"
            @current-change="getData"
          />
        </div>
        <div v-for="(item, index) in data" :key="index">
          <div v-if="isLoading">Loading...</div>
          <div v-if="!isLoading">
            <p @click="toggleTop">================</p>

            <div v-html="item.content.replace(/&nbsp;/g, '').replace(/<p>\s*/g, '<p>')"></div>
            <!-- <p @click="toggleTop" style="color: #c45656">================</p> -->
            <div v-for="(childItem, index) in item.timu" :key="index">
              <div class="item">
                <p v-if="index !== 0">{{ '&nbsp' }}</p>

                <p v-html="childItem.content"></p>
                <div class="item" @click="togglean">
                  <div v-if="anstype">
                    <div
                      v-if="
                        childItem.answerthree.length < 10 || childItem.answerthree.match('image')
                      "
                    >
                      <p>
                        <span class="an_a" v-html="childItem.answerone"></span
                        >{{ '&nbsp;'.repeat(3) }}
                        <span class="an_b" v-html="childItem.answertwo"></span
                        >{{ '&nbsp;'.repeat(3) }}
                        <span class="an_c" v-html="childItem.answerthree"></span
                        >{{ '&nbsp;'.repeat(3) }}
                        <span class="an_d" v-html="childItem.answerfour"></span>
                      </p>
                    </div>

                    <div v-else-if="childItem.answerthree.length <= 16">
                      <p>
                        <span class="an_a" v-html="childItem.answerone"></span
                        >{{ '&nbsp;'.repeat(3) }}
                        <span class="an_b" v-html="childItem.answertwo"></span>
                      </p>
                      <p>
                        <span class="an_c" v-html="childItem.answerthree"></span
                        >{{ '&nbsp;'.repeat(3) }}
                        <span class="an_d" v-html="childItem.answerfour"></span>
                      </p>
                    </div>

                    <div v-else>
                      <p
                        class="an_a"
                        @click="pushans(item.id, 'A')"
                        v-html="childItem.answerone"
                      ></p>
                      <p
                        class="an_b"
                        @click="pushans(item.id, 'B')"
                        v-html="childItem.answertwo"
                      ></p>
                      <p
                        class="an_c"
                        @click="pushans(item.id, 'C')"
                        v-html="childItem.answerthree"
                      ></p>
                      <p
                        class="an_d"
                        @click="pushans(item.id, 'D')"
                        v-html="childItem.answerfour"
                      ></p>
                    </div>
                  </div>
                  <div v-if="!anstype">
                    <p @click="pushans(item.id, 'A')" v-html="childItem.answerone"></p>
                    <p @click="pushans(item.id, 'B')" v-html="childItem.answertwo"></p>
                    <p @click="pushans(item.id, 'C')" v-html="childItem.answerthree"></p>
                    <p @click="pushans(item.id, 'D')" v-html="childItem.answerfour"></p>
                  </div>
                </div>
              </div>

              <div v-show="showContent" class="answer">
                <div @click="open_comment(childItem.id)">
                  {{ childItem.source }}{{ childItem.createdTime
                  }}{{ '正确率:' + Math.round(childItem.correctRatio)
                  }}{{ '易错项:' + childItem.mostWrongAnswer }}
                  <span v-for="childItem in item.timu">{{
                    +childItem.answer === 0
                      ? 'A'
                      : +childItem.answer === 1
                        ? 'B'
                        : +childItem.answer === 2
                          ? 'C'
                          : 'D'
                  }}</span>
                </div>
                <div v-html="childItem.solution"></div>
                <p></p>
                <div>
                  <dp1 v-if="showvideo" :url="childItem.id" :uid="childItem.id"></dp1>
                </div>
              </div>
              <div v-if="index === 1">
                <!-- <p @click="toggleTop" style="color: #00ff05">
                  ================
                </p> -->
                <!-- <div v-html="item.content"></div> -->
                <!--                <p @click="toggleTop" style="color: #00ff05">==============</p>-->
              </div>
            </div>

            <p style="color: #00ff05"></p>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  const data = ref([]);
  const isLoading = ref(true);
  const current1 = ref(1);
  const showContent = ref(false);
  const anstype = ref(true);
  const pagetotal = ref(20);
  const pageSize3 = ref(1);
  const showTop = ref(true);
  const total = ref(204);
  const isdangerarr = ref([false, false, false, false]);
  const route = useRoute();
  const fontStyle = ref(16);
  const biaoming = ref('fb5000m');
  const showvideo = ref(false);
  const open_comment = (id) => {
    window.open(
      '/fb/comment?id=' + id + `&type=` + 48644,
      '_blank',
      'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=800,height=600',
    );
  };
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const increaseFontSize = () => {
    fontStyle.value += 2;
    updateFontSize();
  };
  const updateFontSize = () => {
    const wpElement = document.querySelector('.wp');
    wpElement.style.fontSize = `${fontStyle.value}px`;
  };
  const handleKeyDown = async (event) => {
    if (event.key === 'q') {
      if (current1.value > 1) {
        current1.value -= 1;
        await getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);
          await ansblack();
          showContent.value = false;
        }
      }
    } else if (event.key === 'e') {
      if (current1.value < 10000) {
        current1.value += 1;
        await getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);
          await ansblack();
          showContent.value = false;
        }
      }
    }
  };

  const right = () => {
    if (current1.value < 10000) {
      current1.value += 1;
      getData();
      ansblack();

      if (showContent.value) {
        isDanger(false);

        showContent.value = false;
      }
    }
  };

  const left = () => {
    if (current1.value > 1) {
      current1.value -= 1;
      getData();
      ansblack();

      if (showContent.value) {
        isDanger(false);

        showContent.value = false;
      }
    }
  };
  const toggleContent = (event) => {
    if (event.key === 'w' || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      if (showContent.value) {
        // ansblack();
        isDanger(false);
        showContent.value = false;
      } else {
        // ansred();
        const showAnsButton = document.querySelector('.showans');
        showAnsButton.click();
        showContent.value = true;
      }
    }
  };

  const ansred = async () => {
    await ansblack();
    for (let i = 0; i < data.value.length; i++) {
      // console.log(data.value);
      if (
        data.value &&
        data.value[i].choice === 'A' &&
        document.getElementsByClassName('an_a')[i]
      ) {
        document.getElementsByClassName('an_a')[i].style.color = 'blue';
      }
      if (
        data.value &&
        data.value[i].choice === 'B' &&
        document.getElementsByClassName('an_b')[i]
      ) {
        document.getElementsByClassName('an_b')[i].style.color = 'blue';
      }
      if (
        data.value &&
        data.value[i].choice === 'C' &&
        document.getElementsByClassName('an_c')[i]
      ) {
        document.getElementsByClassName('an_c')[i].style.color = 'blue';
      }
      if (
        data.value &&
        data.value[i].choice === 'D' &&
        document.getElementsByClassName('an_d')[i]
      ) {
        document.getElementsByClassName('an_d')[i].style.color = 'blue';
      }
      if (
        data.value &&
        data.value[i].answer === 'A' &&
        document.getElementsByClassName('an_a')[i]
      ) {
        document.getElementsByClassName('an_a')[i].style.color = 'red';
      }
      if (
        data.value &&
        data.value[i].answer === 'B' &&
        document.getElementsByClassName('an_b')[i]
      ) {
        document.getElementsByClassName('an_b')[i].style.color = 'red';
      }
      if (
        data.value &&
        data.value[i].answer === 'C' &&
        document.getElementsByClassName('an_c')[i]
      ) {
        document.getElementsByClassName('an_c')[i].style.color = 'red';
      }
      if (
        data.value &&
        data.value[i].answer === 'D' &&
        document.getElementsByClassName('an_d')[i]
      ) {
        document.getElementsByClassName('an_d')[i].style.color = 'red';
      }
    }
  };

  const ansblack = async () => {
    for (let i = 0; i < data.value.length; i++) {
      if (document.getElementsByClassName('an_a')[i]) {
        document.getElementsByClassName('an_a')[i].style.color = 'black';
        document.getElementsByClassName('an_b')[i].style.color = 'black';
        document.getElementsByClassName('an_c')[i].style.color = 'black';
        document.getElementsByClassName('an_d')[i].style.color = 'black';
      }
    }
  };

  const togglean = async (answer) => {
    if (showContent.value) {
      await ansblack();
      isDanger(false);
      showContent.value = false;
    } else {
      await ansblack();
      await ansred();
      isDanger(answer);
      showContent.value = true;
    }
  };

  const pushans = async (answer) => {
    // const url = '/egg/minchoice';
    // data.value.choice = answer;
    // const response = await axios.post(url, data.value);
  };

  const isDanger = (answer) => {
    if (data.value && data.value.referenceAnswer === answer && answer !== false) {
      switch (answer) {
        case 'A':
          isdangerarr.value[0] = true;
          break;
        case 'B':
          isdangerarr.value[1] = true;
          break;
        case 'C':
          isdangerarr.value[2] = true;
          break;
        case 'D':
          isdangerarr.value[3] = true;
          break;
      }
      return true;
    } else if (data.value && answer === false) {
      isdangerarr.value.fill(false);
    }
    return false;
  };

  const getData = async () => {
    const per = route.query.per || pageSize3.value;
    const a = route.query.a || false;
    const id = route.query.id || 48905;
    const page = route.query.page || current1.value;
    const type = route.query.type || 'gwy';
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/fbzlfx5000';
    let params = {
      per: per,
      page: page,
      id: id,
      type: type,
      biao: biaoming.value,
    };
    await ansblack();
    try {
      const response = await axios.get(url, { params });
      if (a) {
        showContent.value = true;
      }
      data.value = response.data;
      let x = (page - 1) * per + 1;
      for (let item of data.value) {
        item.content = item.content.replace('<p>', `<p>${x}.`);
        let timu = item.timu;
        let y = 1;
        for (let m of timu) {
          m.content =
            `${y}.(${Math.round(m.correctRatio)}%)` +
            m.content.replace(/<p>/g, '').replace(/<\/p>/g, '');
          y++;
          m.answerone = `A.` + m.answerone.replace(/<p>/g, '').replace(/<\/p>/g, '');
          m.answertwo = `B.` + m.answertwo.replace(/<p>/g, '').replace(/<\/p>/g, '');
          m.answerthree = `C.` + m.answerthree.replace(/<p>/g, '').replace(/<\/p>/g, '');
          m.answerfour = `D.` + m.answerfour.replace(/<p>/g, '').replace(/<\/p>/g, '');
        }
        x++;
      }
      showContent.value = false;
      isLoading.value = false;
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', toggleContent);
    getData();
    window.addEventListener('keydown', handleKeyDown);
    updateFontSize();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
  });
</script>

<style scoped>
  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }
</style>
