<template>
  <a-row>
    <a-col :span="5"></a-col>
    <a-col :span="14">
      <a-textarea
        v-model:value="timu"
        placeholder="题目"
        :auto-size="{ minRows: 2 }"
        @blur="fenxi()"
      />

      <a-row style="margin-top: 20px">
        <a-col :span="1">A:</a-col>
        <a-col :span="23">
          <a-input v-model:value="A" />
        </a-col>
      </a-row>

      <a-row style="margin-top: 20px">
        <a-col :span="1">B:</a-col>
        <a-col :span="23">
          <a-input v-model:value="B" />
        </a-col>
      </a-row>
      <a-row style="margin-top: 20px">
        <a-col :span="1">C:</a-col>
        <a-col :span="23">
          <a-input v-model:value="C" />
        </a-col>
      </a-row>
      <a-row style="margin-top: 20px">
        <a-col :span="1">D:</a-col>
        <a-col :span="23">
          <a-input v-model:value="D" />
        </a-col>
      </a-row>
      <a-textarea
        v-model:value="jiexi"
        placeholder="解析"
        style="margin-top: 20px"
        :auto-size="{ minRows: 2 }"
        @blur="fenxians()"
      />
      <a-row :span="24" style="display: flex; align-items: center">
        <a-col :span="6"
          >正确答案：
          <a-input v-model:value="zhengque" />
        </a-col>
        <a-col :span="2"> </a-col>
        <a-col :span="6"
          >ID:
          <a-input v-model:value="ID" />
        </a-col>
        <a-col :span="2"> </a-col>
        <a-col :span="8">
          <div>模块:</div>
          <a-input v-model:value="moduleName" />
        </a-col>
      </a-row>

      <a-button type="primary" style="margin-top: 20px" @click="pushtimu">Push</a-button>
      <a-button type="primary" style="margin-top: 20px; margin-left: 20px" @click="reset()"
        >Reset</a-button
      >
    </a-col>
    <a-col :span="5"></a-col>
  </a-row>
</template>

<script>
  import { defineComponent, onMounted, ref } from 'vue';
  import { message } from 'ant-design-vue';
  import axios from 'axios';

  export default defineComponent({
    name: 'Timu',
    setup() {
      const timu = ref('');
      const ID = ref(0);
      const A = ref('');
      const B = ref('');
      const C = ref('');
      const D = ref('');
      const zhengque = ref('');
      const jiexi = ref('');
      const moduleName = ref('言语理解');

      const fenxi = () => {
        const regex = /[A-D]\.\s+(.+)/g;
        let matches;
        const options = [];

        while ((matches = regex.exec(timu.value)) !== null) {
          options.push(matches[1]);
        }
        const question = timu.value.replace(regex, '');

        const [optionA, optionB, optionC, optionD] = options;
        timu.value = question.trim();
        A.value = 'A.' + optionA;
        B.value = 'B.' + optionB;
        C.value = 'C.' + optionC;
        D.value = 'D.' + optionD;
      };

      const fenxians = () => {
        const regex = /【答案】([A-Za-z])/;

        const match = jiexi.value.match(regex);
        if (match) {
          const answer = match[1];
          zhengque.value = answer;
        } else {
          console.log('Answer not found.');
        }
      };

      const pushtimu = () => {
        try {
          axios
            .post('/egg/intimu', {
              timu: timu.value,
              ID: ID.value,
              A: A.value,
              B: B.value,
              C: C.value,
              D: D.value,
              zhengque: zhengque.value,
              jiexi: jiexi.value,
              moduleName: moduleName.value,
            })
            .then((res) => {
              message.success('成功添加' + res.data.affectedRows + '条');
              console.log(res.data);
              getId();
            })
            .catch((err) => {
              message.error('This is an error message: ' + err.response.data.data);
              console.log(err);
            });
        } catch (err) {
          message.error('This is an error message');
        }
      };

      const getId = () => {
        try {
          axios.get('/egg/intimu?id=1').then((res) => {
            console.log(res.data);
            ID.value = res.data.lastid + 1;
          });
        } catch (err) {}
      };

      const reset = () => {
        timu.value = '';
        A.value = '';
        B.value = '';
        C.value = '';
        D.value = '';
        zhengque.value = '';
        jiexi.value = '';
      };

      onMounted(() => {
        getId();
      });
      return {
        timu,
        ID,
        A,
        B,
        C,
        D,
        zhengque,
        jiexi,
        moduleName,
        fenxi,
        fenxians,
        pushtimu,
        getId,
        reset,
      };
    },
  });
</script>

<style scoped>
  div,
  input,
  textarea {
    font-size: 20px;
  }
  textarea {
    border-radius: 20px;
  }
  input {
    border-radius: 62px;
  }
</style>
