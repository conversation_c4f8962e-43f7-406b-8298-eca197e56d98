<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <button type="button" class="top_button" :style="buttonStyles" @click="scrollToTop">
          ⬆️ 返回顶部
        </button>
        <div v-show="showTop" class="top">
          <a-space>
            <a-button @click="increaseFontSize">增大字体</a-button>
            <a-button class="showans" @click="togglean">显示答案</a-button>
            <a-button>{{ total }}</a-button>
            <a-button @click="toggleTop">隐藏顶部内容</a-button>
            <fbhiddentag></fbhiddentag>
          </a-space>
          <a-space>
            <a-select
              ref="select"
              v-model:value="value1"
              style="width: 120px"
              @focus="focus"
              @change="handleChange"
            >
              <a-select-option value="48644">gwy</a-select-option>
              <a-select-option value="656604">sy</a-select-option>
            </a-select>
            <a-input v-model:value="timu" style="width: 220px" @change="getData"></a-input>
            <a-button type="primary" @click="ss">粘贴</a-button>
            <a-button type="primary" @click="pushans">传送</a-button>
          </a-space>
        </div>
        <div>
          <div v-html="materials"></div>
          <div v-for="(item, index) in data.solutions" :key="index">
            <div v-if="isLoading">Loading...</div>
            <div v-if="!isLoading" class="contentx">
              <div v-html="item.content"></div>
              <div class="item" @click="togglean">
                <p
                  class="an_a"
                  v-html="
                    `A.` + item.accessories[0].options[0].replace(`fenbike.cn`, `fbstatic.cn`)
                  "
                ></p>
                <p
                  class="an_b"
                  v-html="
                    `B.` + item.accessories[0].options[1].replace(`fenbike.cn`, `fbstatic.cn`)
                  "
                ></p>
                <p
                  class="an_c"
                  v-html="
                    `C.` + item.accessories[0].options[2].replace(`fenbike.cn`, `fbstatic.cn`)
                  "
                ></p>
                <p
                  class="an_d"
                  v-html="
                    `D.` + item.accessories[0].options[3].replace(`fenbike.cn`, `fbstatic.cn`)
                  "
                ></p>
              </div>
              <p @click="toggleTop">====================================</p>
              <div v-show="showContent" class="answer">
                <!--              <div v-html="item.answer"></div>-->
                <div style="color: red" @click="open_comment(item.id)">
                  {{ item.source }}{{ dayjs(item.createdTime).format('YYYY-MM-DD HH:MM:ss') }}
                </div>
                <br />
                <div v-html="item.solution"></div>
                <p>====================================</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup="">
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';
  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';

  const zltimu = ref([]);
  const data = ref([]);
  const isLoading = ref(true);
  const showTop = ref(true);
  const current1 = ref(1);
  const showContent = ref(false);
  const pagetotal = ref(20);
  const pageSize3 = ref(1);
  const total = ref(0);
  const isdangerarr = ref([false, false, false, false]);
  const route = useRoute();
  const fontStyle = ref(16);
  const value1 = ref(48644);
  const timu = ref('');
  const materials = ref('');
  const timuid = ref(0);
  const open_comment = (id) => {
    window.open(
      '/fb/comment?id=' + id + `&type=` + value1.value,
      '_blank',
      'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=800,height=600',
    );
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  const buttonStyles = ref({
    position: 'fixed',
    bottom: '20px',
    left: '50%',
    transform: 'translateX(-50%)',
    zIndex: '1000',
    padding: '10px',
    backgroundColor: '#000',
    color: '#fff',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    boxShadow: '0px 4px 6px rgba(0,0,0,0.1)',
  });
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const increaseFontSize = () => {
    fontStyle.value += 2;
    updateFontSize();
  };
  const updateFontSize = () => {
    const wpElement = document.querySelector('.wp');
    wpElement.style.fontSize = `${fontStyle.value}px`;
  };
  const handleKeyDown = (event) => {
    if (event.key === 'ArrowLeft' || event.key === 'q') {
      getData();
    } else if (event.key === 'ArrowRight' || event.key === 'e') {
      copyText();
    }
  };

  const right = () => {
    if (current1.value < 10000) {
      current1.value += 1;
      getData();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };

  const left = () => {
    if (current1.value > 1) {
      current1.value -= 1;
      getData();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };
  const toggleContent = (event) => {
    if (
      event.key === ' ' ||
      event.key === 'Spacebar' ||
      event.key === 'w' ||
      event.key === 'ArrowUp' ||
      event.key === 'ArrowDown'
    ) {
      if (showContent.value) {
        ansblack();
        isDanger(false);
        showContent.value = false;
      } else {
        ansred();
        const showAnsButton = document.querySelector('.showans');
        showAnsButton.click();
        showContent.value = true;
      }
    }
  };

  const ansred = () => {
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'red';
      }
    }
  };

  const ansblack = () => {
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'black';
      }
    }
  };

  const togglean = (answer) => {
    if (showContent.value) {
      ansblack();
      isDanger(false);
      showContent.value = false;
    } else {
      ansred();
      isDanger(answer);
      showContent.value = true;
    }
  };

  const pushans = async (answer) => {
    const url = '/egg/fbupdate5000zlfx';
    const response = await axios.get(url, {
      params: {
        dataid: data.value.dataid,
        checkId: data.value.checkId,
        type: value1.value,
      },
    });
    if (response.data.message.affectedRows) {
      message.success('创建成功');
    }
  };
  const copyText = async () => {
    const url = '/egg/fbupdate5000';
    let params = {
      id: timuid.value,
    };
    console.log(params);

    const response = await axios.get(url, { params });
    if (response.status === 200) {
      message.success('创建成功');
    }
    console.log(response);
  };

  const isDanger = (answer) => {
    if (data.value && data.value.answer === answer && answer !== false) {
      switch (answer) {
        case 'A':
          isdangerarr.value[0] = true;
          break;
        case 'B':
          isdangerarr.value[1] = true;
          break;
        case 'C':
          isdangerarr.value[2] = true;
          break;
        case 'D':
          isdangerarr.value[3] = true;
          break;
      }
      return true;
    } else if (data.value && answer === false) {
      isdangerarr.value.fill(false);
    }
    return false;
  };
  const focus = () => {
    console.log('focus');
  };
  const handleChange = (value) => {
    console.log(`selected ${value}`);
  };
  const ss = async () => {
    timu.value = await navigator.clipboard.readText();
    await getData();
  };
  const getData = async () => {
    const a = route.query.a || false;
    const z = route.query.z || 0;
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/fbq';
    let params = {
      q: timu.value,
      type: value1.value,
    };
    console.log(params);
    try {
      const response = await axios.get(url, { params });
      if (response.data.length === 0) {
        console.log('z1', z);
        await getData();
        return;
      }

      data.value = response.data;
      materials.value = data.value.materials[0].content;
      console.log(materials.value);

      showContent.value = false;
      isLoading.value = false;
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', toggleContent);
    // getData();
    window.addEventListener('keydown', handleKeyDown);
    updateFontSize();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
  });
</script>

<style scoped>
  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }

  .taginput {
    user-select: none;
    cursor: pointer;
  }
</style>
