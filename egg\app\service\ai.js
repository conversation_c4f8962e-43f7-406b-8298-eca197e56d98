const Service = require('egg').Service;
const axios = require('axios');
const OpenAI = require('openai');
const path = require('path');
const puppeteer = require('puppeteer');
const { dayNow, dateNow } = require('../extend/helper');
const fs = require('fs');
const os = require('os');
const tencentcloud = require('tencentcloud-sdk-nodejs');
const OcrClient = tencentcloud.ocr.v20181119.Client;
const FormData = require('form-data');
const { createParser } = require('eventsource-parser');
const sharp = require('sharp');
const { logger } = require('../../config/config.default');
const openai = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: '***********************************',
  // apiKey: 'sk-695c2c83c3f740c684bff8ac73eac5ea',
});
const siliai = new OpenAI({
  baseURL: 'https://api.siliconflow.cn/v1',
  apiKey: 'sk-tqeffovhnxuhojxzzbitlzhgpfguaqredvigjzjjybbumvsf',
});
const key = {
  secretId: 'AKIDAygNAC2uWtnzzzsvL9r0uTFmBvV62oqD',
  secretKey: 'O0J55pBixsNyMw97i1z3xcI71FJXXkkv',
};

class AiService extends Service {
  async mathocr(base64) {
    const { ctx, app } = this;
    const clientConfig = {
      credential: key,
      region: 'ap-guangzhou',
      profile: {
        httpProfile: {
          endpoint: 'ocr.tencentcloudapi.com',
        },
      },
    };

    try {
      const client = new OcrClient(clientConfig);
      const params = {
        ImageBase64: base64,
      };
      let text = await client.EduPaperOCR(params).then(
        (data) => {
          // console.log(data);
          let x = '';
          for (let item of data.EduPaperInfos) {
            x += item.DetectedText;
          }
          // console.log(x);
          return x;
        },
        (err) => {
          console.error('error', err);
        },
      );
      // console.log(text);

      return { text: text };
    } catch (err) {
      console.error('error', err);
    }
  }

  async index(content) {
    const { ctx } = this;
    try {
      // console.log(content);
      const completion = await openai.chat.completions.create({
        messages: [
          {
            role: 'user',
            content: content,
          },
        ],
        model: 'deepseek-reasoner',
      });

      // console.log(completion.choices[0].message.content);
      return {
        data: completion.choices[0].message.content,
        status: 200,
      };
    } catch (e) {
      console.error(e.message, e.status);
    }
  }

  async sili1(content, itemid, model) {
    const { ctx, app } = this;
    const redisKey = 'sili:count';

    try {
      const result = await app.redis.eval(
        `
      local count = redis.call('INCR', KEYS[1])
      if count > tonumber(ARGV[1]) then
        return -1
      else
        return count
      end
      `,
        1, // 键的数量
        redisKey, // 键名
        6, // 最大限制
      );

      // console.log(result);
      if (result === -1) {
        return {
          data: '超过了',
          status: 400,
        };
      }

      await ctx.service.xr['update']('fbsy', {
        id: itemid,
        ds: 'ds',
        choice: +model,
      });

      const completion = await siliai.chat.completions.create({
        messages: [{ role: 'user', content }],
        model: 'deepseek-ai/DeepSeek-R1',
      });
      // await new Promise((resolve) => setTimeout(resolve, 2000));
      return {
        data: completion.choices[0].message.content,
        status: 200,
      };
    } catch (e) {
      console.error(e.message, e.status);
      return {
        data: '出错了',
        status: 500,
      };
    } finally {
      // 使用Lua脚本原子性地减少计数器
      await app.redis.eval(
        `
      local count = redis.call('GET', KEYS[1])
      if count and tonumber(count) > 0 then
        return redis.call('DECR', KEYS[1])
      else
        return 0
      end
      `,
        1, // 键的数量
        redisKey, // 键名
      );
    }
  }

  async tuili(content) {
    const { ctx } = this;
    try {
      console.log('User content:', content);

      const completion = await openai.chat.completions.create({
        messages: [
          {
            role: 'user',
            content,
          },
        ],
        model: 'deepseek-reasoner',
        stream: true,
      });

      let reasoningContent = '';
      let finalContent = '';
      let prefixPrinted = false;

      // Helper for typing effect
      const typeWriter = async (text) => {
        for (const char of text) {
          process.stdout.write(char);
          await new Promise((resolve) => setTimeout(resolve, 10)); // 10ms delay
        }
      };

      for await (const chunk of completion) {
        const delta = chunk.choices[0].delta;

        if (delta.reasoning_content) {
          if (!prefixPrinted) {
            process.stdout.write('\n[Thinking] ');
            prefixPrinted = true;
          }
          const reasoningChunk = delta.reasoning_content;
          reasoningContent += reasoningChunk;
          await typeWriter(reasoningChunk);
        }

        if (delta.content) {
          finalContent += delta.content;
        }
      }

      if (prefixPrinted) {
        process.stdout.write('\n\n'); // Add newlines for separation
      }

      console.log('[Final Answer]:');
      console.log(finalContent);

      return {
        data: finalContent,
        reasoning_content: reasoningContent,
        status: 200,
      };
    } catch (e) {
      console.error(e.message, e.status);
      return { status: 500, message: e.message };
    }
  }

  async sili(content) {
    const { ctx, app, logger } = this;
    try {
      const messageContent = [
        {
          type: 'text',
          text: content,
        },
      ];

      let buffer = '';
      let printTimer;
      let reasoningContent = '';
      let finalContent = '';

      const completion = await siliai.chat.completions.create({
        messages: [{ role: 'user', content: messageContent }],
        stream: true,
        model: 'deepseek-ai/DeepSeek-R1',
      });

      const startPrinter = () => {
        if (!printTimer) {
          printTimer = setInterval(() => {
            if (buffer) {
              const char = buffer[0];
              process.stdout.write(char);
              buffer = buffer.slice(1);
            } else {
              clearInterval(printTimer);
              printTimer = null;
            }
          }, 20);
        }
      };

      for await (const chunk of completion) {
        const delta = chunk.choices?.[0]?.delta || {};
        const reasoning = delta.reasoning_content;
        if (reasoning) {
          // writeStream(`data: ${reasoning}\n\n`);
        }
        if (delta.reasoning_content) {
          reasoningContent += delta.reasoning_content;
          buffer += delta.reasoning_content;
          startPrinter();
        }
        const content1 = delta.content;
        if (content1) {
          // writeStream(`data: ${content1}\n\n`);
        }
        if (delta.content) {
          finalContent += delta.content;
          buffer += delta.content;
          startPrinter();
        }
      }
      // writeStream(`event: end\ndata: end\n\n`);
      // 等待打印完成
      await new Promise((resolve) => {
        const check = setInterval(() => {
          if (!buffer && !printTimer) {
            clearInterval(check);
            resolve();
          }
        }, 50);
      });

      return {
        data: finalContent,
        reasoning_content: reasoningContent,
        status: 200,
      };
    } catch (e) {
      console.error(e.message, e.status);
      logger.error(e);
      return {
        data: '手稿',
        status: 200,
      };
    }
  }

  async streamSili(content, writeStream) {
    const { app } = this;
    await app.redis.set('sili', '1');

    try {
      const messageContent = [{ type: 'text', text: content }];
      const completion = await siliai.chat.completions.create({
        messages: [{ role: 'user', content: messageContent }],
        stream: true,
        model: 'deepseek-ai/DeepSeek-R1',
      });

      for await (const chunk of completion) {
        const delta = chunk.choices?.[0]?.delta || {};
        const reasoning = delta.reasoning_content;
        if (reasoning) {
          writeStream(`data: ${reasoning}\n\n`);
        }
      }

      writeStream(`event: end\ndata: end\n\n`);
    } catch (e) {
      console.error(e.message, e.status);
      writeStream(`event: error\ndata: ${e.message}\n\n`);
    } finally {
      await app.redis.set('sili', '0');
    }
  }

  async silipic(content, imagePath) {
    const { ctx } = this;

    function imageToBase64DataURL(filePath) {
      const buffer = fs.readFileSync(filePath);
      const ext = path.extname(filePath).toLowerCase().replace('.', '');
      const mimeType = ext === 'jpg' ? 'jpeg' : ext; // 修正 jpg -> jpeg
      return `data:image/${mimeType};base64,${buffer.toString('base64')}`;
    }

    try {
      const imageDataURL = imageToBase64DataURL(imagePath);
      let x = this.mathocr(imageDataURL);
      return x;

      const completion = await siliai.chat.completions.create({
        model: 'Qwen/Qwen2.5-VL-32B-Instruct',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'image_url',
                image_url: {
                  url: imageDataURL,
                  detail: 'high', // 也可以是 'high'，看具体要求
                },
              },
              {
                type: 'text',
                text: content,
              },
            ],
          },
        ],
      });

      return {
        data: completion.choices[0].message.content,
        status: 200,
      };
    } catch (e) {
      console.error(e.message, e.status);
    }
  }

  async renderHtmlToImage(htmlContent) {
    const { ctx, app } = this;
    const os = require('os');
    const platform = os.platform();

    app.logger.info(`🖥️ 检测到操作系统: ${platform}`);
    console.log(`🖥️ 当前操作系统: ${platform}`);

    // 根据操作系统选择渲染方式
    if (platform === 'win32') {
      // Windows 使用 Puppeteer
      app.logger.info('🪟 Windows环境，使用 Puppeteer 渲染');
      console.log('🪟 使用 Puppeteer 渲染 (Windows)');
      return await this.renderWithPuppeteer(htmlContent);
    } else {
      // Linux/Debian 优先使用 wkhtmltoimage
      app.logger.info('🐧 Linux/Debian环境，优先使用 wkhtmltoimage');
      console.log('🐧 使用 wkhtmltoimage 渲染 (Linux/Debian)');

      try {
        const result = await this.renderWithWkhtmltoimage(htmlContent);
        if (result.success) {
          return await this.processImageForOCR(result.imagePath);
        }
      } catch (error) {
        app.logger.warn('wkhtmltoimage 失败，降级到 Puppeteer:', error.message);
        console.log('⚠️ wkhtmltoimage 失败，降级到 Puppeteer');
        return await this.renderWithPuppeteer(htmlContent);
      }
    }
  }

  // 新增：专门为timutoextra提供的渲染方法，返回base64图片数据
  async renderHtmlToImageBase64(htmlContent) {
    const { ctx, app } = this;
    const os = require('os');
    const platform = os.platform();

    app.logger.info(`🖥️ 检测到操作系统: ${platform}`);
    console.log(`🖥️ 当前操作系统: ${platform}`);

    // 根据操作系统选择渲染方式
    if (platform === 'win32') {
      // Windows 使用 Puppeteer
      app.logger.info('🪟 Windows环境，使用 Puppeteer 渲染');
      console.log('🪟 使用 Puppeteer 渲染 (Windows)');
      return await this.renderWithPuppeteerBase64(htmlContent);
    } else {
      // Linux/Debian 优先使用 wkhtmltoimage
      app.logger.info('🐧 Linux/Debian环境，优先使用 wkhtmltoimage');
      console.log('🐧 使用 wkhtmltoimage 渲染 (Linux/Debian)');

      try {
        console.log('🔧 尝试使用 wkhtmltoimage 渲染...');
        const result = await this.renderWithWkhtmltoimageBase64(htmlContent);
        if (result.success) {
          console.log('✅ wkhtmltoimage 渲染成功');
          return result;
        } else {
          console.log('❌ wkhtmltoimage 返回失败状态:', result);
        }
      } catch (error) {
        app.logger.warn('wkhtmltoimage 失败，降级到 Puppeteer:', error.message);
        console.log('❌ wkhtmltoimage 失败，降级到 Puppeteer');
        console.log('❌ wkhtmltoimage 错误详情:', error.stack || error.message);
        console.log('❌ wkhtmltoimage 错误类型:', error.constructor.name);

        // 检查是否是 wkhtmltoimage 未安装
        if (error.message.includes('spawn wkhtmltoimage ENOENT')) {
          console.log('⚠️ wkhtmltoimage 未安装或不在 PATH 中');
        }

        console.log('🔄 开始使用 Puppeteer 降级渲染...');
        return await this.renderWithPuppeteerBase64(htmlContent);
      }
    }
  }

  // Puppeteer渲染返回base64数据
  async renderWithPuppeteerBase64(htmlContent) {
    const { app } = this;

    // 启动优化的浏览器
    const browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--single-process',
        '--memory-pressure-off',
        '--max_old_space_size=256',
      ],
      timeout: 30000,
    });

    const page = await browser.newPage();
    await page.setViewport({
      width: 800,
      height: 600,
      deviceScaleFactor: 1,
    });

    // 优化的HTML内容（中文字体优化）
    const decoratedHtml = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8" />
      <style>
        body {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", "Source Han Sans CN", "Microsoft YaHei", "SimHei", Arial, sans-serif;
          line-height: 1.6;
          margin: 20px;
          font-size: 16px;
          background: white;
          color: black;
        }
        img {
          max-width: 100%;
          height: auto;
          display: block;
          margin: 10px 0;
          border: 1px solid #ddd;
          border-radius: 4px;
        }
        .material {
          margin-bottom: 20px;
          padding: 15px;
          background: #f8f9fa;
          border-left: 4px solid #007bff;
          border-radius: 4px;
        }
        .question {
          margin-bottom: 20px;
          font-weight: bold;
          font-size: 18px;
        }
        .options {
          margin: 15px 0;
        }
        .option {
          margin: 8px 0;
          padding: 8px;
          background: #f5f5f5;
          border-radius: 4px;
        }
        .answer {
          margin-top: 20px;
          padding: 10px;
          background: #d4edda;
          border-left: 4px solid #28a745;
          border-radius: 4px;
          font-weight: bold;
        }
        .solution {
          margin-top: 15px;
          padding: 10px;
          background: #e2e3e5;
          border-left: 4px solid #6c757d;
          border-radius: 4px;
        }
        h1, h2, h3, h4, h5, h6 {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
          color: #333;
        }
        p, div, span {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
        }
      </style>
    </head>
    <body>
      ${htmlContent}
    </body>
    </html>
    `;

    try {
      // 设置内容并等待图片加载
      await page.setContent(decoratedHtml, {
        waitUntil: 'networkidle0', // 等待网络空闲，确保图片加载完成
        timeout: 30000, // 增加超时时间
      });

      // 等待图片加载完成
      await page
        .waitForFunction(
          () => {
            const images = Array.from(document.images);
            return images.every((img) => img.complete);
          },
          { timeout: 15000 },
        )
        .catch(() => {
          console.log('⚠️ 部分图片可能未完全加载');
        });

      // 设置保存路径
      const os = require('os');
      const platform = os.platform();
      const isWindows = platform === 'win32';
      const saveDir = isWindows ? 'C:/Users/<USER>/Desktop/1/' : '/tmp/';

      // 确保目录存在
      const fs = require('fs');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
        console.log(`📁 创建目录: ${saveDir}`);
      }

      const timestamp = Date.now();
      const imagePath = path.resolve(saveDir, `puppeteer_${timestamp}.jpg`);

      console.log('📸 开始高质量截图保存...');

      // 生成高质量版本 (95%质量)
      console.log('📷 正在生成高质量截图...');
      const imageBuffer = await page.screenshot({
        fullPage: true,
        type: 'jpeg',
        quality: 95,
      });

      // 保存到文件
      fs.writeFileSync(imagePath, imageBuffer);
      const imageSize = imageBuffer.length;
      console.log(
        `✅ 高质量截图已保存 - 质量:95%, 大小:${(imageSize / 1024 / 1024).toFixed(2)}MB`,
      );
      console.log(`📁 截图文件: ${imagePath}`);

      await browser.close();

      // 转换为base64（用于AI处理）
      const base64Image = imageBuffer.toString('base64');

      return {
        status: 200,
        base64: base64Image,
        mimeType: 'image/jpeg',
        message: 'Puppeteer高质量渲染成功',
        filePath: imagePath,
        quality: 95,
        fileSize: imageSize,
      };
    } catch (error) {
      await browser.close();
      throw error;
    }
  }

  // 使用 wkhtmltoimage 渲染（推荐方案）
  async renderWithWkhtmltoimage(htmlContent) {
    const { spawn } = require('child_process');
    const platform = os.platform();
    const isWindows = platform === 'win32';
    const saveDir = isWindows ? 'C:/Users/<USER>/Desktop/1/' : '/tmp/';

    const timestamp = Date.now();
    const htmlFile = path.resolve(saveDir, `temp_${timestamp}.html`);
    const imageFile = path.resolve(saveDir, `wkhtml_${timestamp}.png`);

    // 创建优化的HTML内容（中文字体优化）
    const decoratedHtml = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8" />
      <style>
        body {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", "Source Han Sans CN", "Microsoft YaHei", "SimHei", Arial, sans-serif;
          line-height: 1.6;
          margin: 20px;
          font-size: 16px;
          background: white;
          color: black;
        }
        img { max-width: 100%; height: auto; }
        .question { margin-bottom: 20px; }
        .options { margin: 10px 0; }
        .solution { margin-top: 20px; border-top: 1px solid #ccc; padding-top: 10px; }
        h1, h2, h3, h4, h5, h6 {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
          color: #333;
        }
        p, div, span {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
        }
      </style>
    </head>
    <body>
      ${htmlContent}
    </body>
    </html>
    `;

    // 写入临时HTML文件
    fs.writeFileSync(htmlFile, decoratedHtml);

    return new Promise((resolve, reject) => {
      const args = [
        '--width',
        '800',
        '--height',
        '600',
        '--format',
        'png',
        '--quality',
        '90',
        '--disable-javascript',
        '--disable-plugins',
        '--load-error-handling',
        'ignore', // 忽略图片加载错误，但允许加载
        '--quiet',
        htmlFile,
        imageFile,
      ];

      const child = spawn('wkhtmltoimage', args);
      let stderr = '';

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        // 清理临时HTML文件
        try {
          if (fs.existsSync(htmlFile)) fs.unlinkSync(htmlFile);
        } catch (e) {
          // 忽略清理错误
        }

        if (code === 0 && fs.existsSync(imageFile)) {
          resolve({ success: true, imagePath: imageFile });
        } else {
          reject(
            new Error(`wkhtmltoimage failed with code ${code}: ${stderr}`),
          );
        }
      });

      child.on('error', (error) => {
        // 清理临时文件
        try {
          if (fs.existsSync(htmlFile)) fs.unlinkSync(htmlFile);
        } catch (e) {
          // 忽略清理错误
        }
        reject(error);
      });
    });
  }

  // wkhtmltoimage渲染返回base64数据
  async renderWithWkhtmltoimageBase64(htmlContent) {
    const { spawn } = require('child_process');
    const platform = os.platform();
    const isWindows = platform === 'win32';
    const saveDir = isWindows ? 'C:/Users/<USER>/Desktop/1/' : '/tmp/';

    const timestamp = Date.now();
    const htmlFile = path.resolve(saveDir, `temp_${timestamp}.html`);
    const imageFile = path.resolve(saveDir, `wkhtml_${timestamp}.png`);

    // 创建优化的HTML内容
    const decoratedHtml = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8" />
      <style>
        body {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", "Source Han Sans CN", "Microsoft YaHei", "SimHei", Arial, sans-serif;
          line-height: 1.6;
          margin: 20px;
          font-size: 16px;
          background: white;
          color: black;
        }
        img { max-width: 100%; height: auto; }
        .question { margin-bottom: 20px; }
        .options { margin: 10px 0; }
        .solution { margin-top: 20px; border-top: 1px solid #ccc; padding-top: 10px; }
        h1, h2, h3, h4, h5, h6 {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
          color: #333;
        }
        p, div, span {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
        }
      </style>
    </head>
    <body>
      ${htmlContent}
    </body>
    </html>
    `;

    // 写入临时HTML文件
    fs.writeFileSync(htmlFile, decoratedHtml);

    return new Promise((resolve, reject) => {
      const args = [
        '--width',
        '1200',
        '--height',
        '0', // 自动高度
        '--format',
        'png',
        '--quality',
        '95',
        '--quiet',
        htmlFile,
        imageFile,
      ];

      const child = spawn('wkhtmltoimage', args);
      let stderr = '';

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', async (code) => {
        // 清理临时HTML文件
        try {
          if (fs.existsSync(htmlFile)) fs.unlinkSync(htmlFile);
        } catch (e) {
          // 忽略清理错误
        }

        if (code === 0 && fs.existsSync(imageFile)) {
          try {
            // 读取图片文件
            let imageBuffer = fs.readFileSync(imageFile);
            const originalSize = imageBuffer.length;
            const maxSize = 2 * 1024 * 1024; // 2MB限制

            console.log(
              `📊 wkhtmltoimage原始大小: ${(originalSize / 1024 / 1024).toFixed(2)}MB`,
            );

            // 如果PNG文件过大，转换为JPEG并压缩
            if (originalSize > maxSize) {
              console.log('🔄 PNG文件过大，转换为JPEG压缩...');

              const sharp = require('sharp');
              let quality = 90;

              // 循环压缩直到满足大小要求
              do {
                imageBuffer = await sharp(imageBuffer)
                  .jpeg({ quality: quality, progressive: true })
                  .toBuffer();

                console.log(
                  `🔍 JPEG质量${quality}% -> 大小: ${(imageBuffer.length / 1024 / 1024).toFixed(2)}MB`,
                );

                if (imageBuffer.length <= maxSize) {
                  break;
                }

                quality -= 10;

                if (quality < 50) {
                  // 如果质量太低，尝试调整尺寸
                  const metadata = await sharp(
                    fs.readFileSync(imageFile),
                  ).metadata();
                  const scaleFactor = Math.sqrt(maxSize / imageBuffer.length);
                  const newWidth = Math.floor(metadata.width * scaleFactor);
                  const newHeight = Math.floor(metadata.height * scaleFactor);

                  imageBuffer = await sharp(fs.readFileSync(imageFile))
                    .resize(newWidth, newHeight)
                    .jpeg({ quality: 70, progressive: true })
                    .toBuffer();

                  console.log(`📐 调整尺寸: ${newWidth}x${newHeight}, 质量70%`);
                  break;
                }
              } while (imageBuffer.length > maxSize);

              console.log(
                `✅ 压缩完成: ${(imageBuffer.length / 1024 / 1024).toFixed(2)}MB`,
              );
            }

            const base64Image = imageBuffer.toString('base64');
            const finalMimeType =
              originalSize > maxSize ? 'image/jpeg' : 'image/png';

            resolve({
              status: 200,
              success: true,
              base64: base64Image,
              mimeType: finalMimeType,
              message: 'wkhtmltoimage渲染成功',
              originalSize: originalSize,
              finalSize: imageBuffer.length,
              compressed: originalSize > maxSize,
            });
          } catch (readError) {
            reject(new Error(`读取图片文件失败: ${readError.message}`));
          }
        } else {
          reject(
            new Error(`wkhtmltoimage failed with code ${code}: ${stderr}`),
          );
        }
      });

      child.on('error', (error) => {
        // 清理临时文件
        try {
          if (fs.existsSync(htmlFile)) fs.unlinkSync(htmlFile);
        } catch (e) {
          // 忽略清理错误
        }
        reject(error);
      });
    });
  }

  // Puppeteer 降级方案（优化版本）
  async renderWithPuppeteer(htmlContent) {
    const { app } = this;

    // 启动优化的浏览器
    const browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage', // 减少内存使用
        '--disable-gpu',
        '--single-process', // 单进程模式，适合低内存环境
        '--memory-pressure-off',
        '--max_old_space_size=256', // 限制内存
      ],
      timeout: 30000,
    });

    const page = await browser.newPage();
    await page.setViewport({
      width: 800, // 减小视口尺寸
      height: 600,
      deviceScaleFactor: 1, // 降低像素密度
    });

    // 优化的HTML内容（中文字体优化）
    const decoratedHtml = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8" />
      <style>
        body {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", "Source Han Sans CN", "Microsoft YaHei", "SimHei", Arial, sans-serif;
          line-height: 1.6;
          margin: 20px;
          font-size: 16px;
          background: white;
          color: black;
        }
        img { max-width: 100%; height: auto; }
        .question { margin-bottom: 20px; }
        .options { margin: 10px 0; }
        .solution { margin-top: 20px; border-top: 1px solid #ccc; padding-top: 10px; }
        h1, h2, h3, h4, h5, h6 {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
          color: #333;
        }
        p, div, span {
          font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
        }
      </style>
    </head>
    <body>
      ${htmlContent}
    </body>
    </html>
    `;

    try {
      // 设置内容（减少等待时间）
      await page.setContent(decoratedHtml, {
        waitUntil: 'domcontentloaded', // 改为更快的等待条件
        timeout: 15000,
      });

      const platform = os.platform();
      const isWindows = platform === 'win32';
      const saveDir = isWindows ? 'C:/Users/<USER>/Desktop/1/' : '/tmp/';
      const timestamp = Date.now();
      const imagePath = path.resolve(saveDir, `puppeteer_${timestamp}.jpg`);

      // 截图（使用JPEG格式支持质量压缩）
      await page.screenshot({
        path: imagePath,
        fullPage: true,
        type: 'jpeg',
        quality: 85, // JPEG格式支持质量参数
      });

      await browser.close();

      return await this.processImageForOCR(imagePath);
    } catch (error) {
      await browser.close();
      throw error;
    }
  }

  // 统一的图片处理和OCR方法
  async processImageForOCR(imagePath) {
    const { app } = this;

    try {
      // 生成处理后的图片路径
      const dir = path.dirname(imagePath);
      const ext = path.extname(imagePath);
      const basename = path.basename(imagePath, ext);
      const processedPath = path.join(dir, `${basename}_processed${ext}`);

      // 使用Sharp进行图像处理（灰度化、二值化）
      await sharp(imagePath)
        .grayscale() // 灰度图
        .gamma(3.0) // gamma 矫正
        .threshold(180) // 二值化
        .toFile(processedPath);

      // 创建 FormData 实例并附加文件
      const form = new FormData();
      form.append('file', fs.createReadStream(processedPath));

      // 构造 headers，带上 multipart 边界
      const headers = {
        ...form.getHeaders(),
      };

      // 发送 POST 请求到 OCR 接口
      const result = await axios
        .post('http://***********:6030/ocr', form, { headers })
        .then((response) => {
          const res = response.data;
          if (res.code === 200) {
            return {
              status: 200,
              text: res.data,
            };
          } else {
            return {
              status: 400,
              message: res.msg,
            };
          }
        })
        .catch((error) => {
          const err = error?.response?.data || error.message;
          app.logger.error('OCR请求失败：', err);
          return {
            status: 500,
            error: err,
          };
        });

      // 不清理临时文件，由定时任务处理
      // try {
      //   if (fs.existsSync(imagePath)) fs.unlinkSync(imagePath);
      //   if (fs.existsSync(processedPath)) fs.unlinkSync(processedPath);
      // } catch (cleanupError) {
      //   app.logger.warn('清理临时文件失败:', cleanupError);
      // }

      return result;
    } catch (error) {
      app.logger.error('图片处理失败:', error);
      // 不清理临时文件，由定时任务处理
      // try {
      //   if (fs.existsSync(imagePath)) fs.unlinkSync(imagePath);
      // } catch (cleanupError) {
      //   // 忽略清理错误
      // }
      throw error;
    }
  }

  /**
   * 图片文字提取方法（非流式）
   * 使用GLM-4.1V-9B-Thinking模型进行图片文字提取
   */
  async extractTextFromImage(base64Image, mimeType) {
    const { app } = this;

    try {
      // 初始化OpenAI客户端
      const OpenAI = require('openai');
      const client = new OpenAI({
        apiKey:
          process.env.SILICONFLOW_API_KEY ||
          'sk-jvyedhzpmkjtqxzcoqyfjhrygncnmylywtumnppkqzqnscqj',
        baseURL: 'https://api.siliconflow.cn/v1',
      });

      // 构建请求消息
      const messages = [
        {
          role: 'user',
          content: [
            {
              type: 'image_url',
              image_url: {
                url: `data:${mimeType};base64,${base64Image}`,
                detail: 'low',
              },
            },
            {
              type: 'text',
              text: '提取文字和表格和柱状图折线图或各种图转为markdown格式到代码块里，提取到题目不做解析，只提取文字和图形整合到markdown里，主要是提取解析，解析一定要提取',
            },
          ],
        },
      ];

      app.logger.info('🖼️ 开始图片文字提取...');
      console.log('🔍 [图片识别] 正在处理...');

      // 发送非流式请求到AI模型
      const response = await client.chat.completions.create({
        model: 'Qwen/QVQ-72B-Preview',
        messages: messages,
        stream: false, // 禁用流式响应
      });

      // 提取响应内容
      const extractedText = response.choices[0].message.content;

      console.log('✅ [图片识别完成]');
      app.logger.info(
        `📝 图片文字提取完成，内容长度: ${extractedText.length} 字符`,
      );

      return {
        status: 200,
        text: extractedText,
        message: '图片文字提取成功',
      };
    } catch (error) {
      app.logger.error('❌ 图片文字提取失败:', error);
      console.log('❌ [图片识别失败]');

      return {
        status: 500,
        error: error.message,
        message: '图片文字提取失败',
      };
    }
  }

  async askQuestion2(message) {
    const { ctx, service, app } = this;
    let cookie = await app.redis.get('zhcookie2');

    const sendHeaders = {
      accept: '*/*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'cache-control': 'no-cache',
      'content-type': 'text/plain;charset=UTF-8',
      pragma: 'no-cache',
      'x-kl-ajax-request': 'Ajax_Request',
      'x-requested-with': 'fetch',
      'x-xsrftoken': 'c0235a19-1cc7-4774-bdd2-7cd1d5a3390a',
      'x-zse-93': '101_3_3.0',
      'x-zse-96':
        '2.0_m=xePqnO3tcnVgVnSIWWwmuNRiT8iitzWmQG7rRW0BttWXpneRnyT3I7DqDwbLzI',
      cookie: cookie,
      Referer: 'https://zhida.zhihu.com/search/3668007421848194238',
    };

    const sendBody = {
      knowledge_ids: [
        'KBT_GLOBAL',
        'KBT_ZHIHU',
        'KBT_PAPER',
        'KBT_PERSONAL_KNOWLEDGE_BASE',
      ],
      quiz_type: 'QT_CHAT',
      attachments: [],
      message_source_type: 'text',
      session_id: '3668007421848194238',
      zhida_source: 'zhida',
      chat_model: 'CM_DEEP_SEEK_R1',
      message_content: message,
    };

    // 发送问题
    try {
      const sendRes = await axios.post(
        'https://zhida.zhihu.com/ai_ingress/ai_chat/send_message_v2',
        sendBody,
        { headers: sendHeaders },
      );

      const recvMessageId = sendRes.data?.recv_message?.message_id;
      if (!recvMessageId) {
        ctx.service.feishu['fs3']('发送失败，未获取 recv_message.message_id');
      }

      // 轮询等待响应
      const pollHeaders = {
        ...sendHeaders,
        'x-zse-96':
          '2.0_nAqKqqSXHwCISPQgyDL/HPZ=S84HId122lw5XV3CG6Ij2YSu68ftj7UqdbSqsIdZ',
      };
      // 用来存放后端推过来的完整 thinking 文本
      let thinkingSoFar = '';
      // 记录上次已经打印到控制台的 thinking 长度
      let lastThinkingLength = 0;
      const answerList = [];
      let done = false;
      const thinkingList = [];
      let thinking = '';
      let prefixPrinted = false;
      // 这个 Promise 链用来串行地打印各个 thinking 片段，
      // 但主循环不会 await 它，所以不会阻塞对 Answer 的判断
      let lastPrintPromise = Promise.resolve();
      // 不 await，这样主循环能马上继续往下走去检查 Answer/End

      // 打字机效果的辅助函数，返回一个 Promise，
      // 会等到这个片段全部打印完才 resolve
      function typeWriterPromise(text) {
        return new Promise(async (resolve) => {
          if (!prefixPrinted) {
            process.stdout.write('[Thinking] ');
            prefixPrinted = true;
          }
          for (const char of text) {
            process.stdout.write(char);
            await new Promise((r) => setTimeout(r, 10));
          }
          resolve();
        });
      }

      while (!done) {
        await new Promise((resolve) => setTimeout(resolve, 1000)); // 每秒轮询一次
        const pollRes = await axios.get(
          `https://zhida.zhihu.com/ai_ingress/ai_chat/polling_message_v2?message_id=${recvMessageId}`,
          { headers: pollHeaders },
        );

        const events = pollRes.data;
        for (const event of events) {
          if (event.event === 'Answer' && event.data?.summary) {
            answerList.push(event.data.summary);
          } else if (event.event === 'Think' && event.data?.thinking) {
            const currentThinking = event.data.thinking;

            // 计算这次刚出现的新增部分
            const diff = currentThinking.slice(lastThinkingLength);
            // 更新已保存的完整 thinking，以及已打印长度
            thinkingSoFar = currentThinking;
            lastThinkingLength = currentThinking.length;

            // 如果 diff 不为空，再排队给打字机效果
            if (diff) {
              lastPrintPromise = lastPrintPromise.then(() =>
                typeWriterPromise(diff),
              );
            }
          } else if (event.event === 'End') {
            done = true;
            break;
          }
        }
      }
      // let answerListnew = [];
      // answerListnew.push(answerList[answerList.length - 1]);
      // return answerListnew;
      return {
        data: answerList[answerList.length - 1],
        status: 200,
      };
    } catch (e) {
      const text =
        e?.response?.data?.error?.message || e?.message || '未知错误';
      ctx.service.feishu['fs']('知乎问答2' + text);
      return {
        message: e.response.data,
        status: 400,
      };
    }
  }

  async askQuestion3(
    message,
    token,
    xone,
    xtwo,
    session_id,
    cookiename,
    model,
    key,
    stop = false,
  ) {
    const { ctx, service, app } = this;
    let cookie = await app.redis.get(cookiename);

    // 初始化思考过程缓存
    const thinkingProcess = [];
    const thinkingAnswer = [];

    const sendHeaders = {
      accept: '*/*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'cache-control': 'no-cache',
      'content-type': 'text/plain;charset=UTF-8',
      pragma: 'no-cache',
      'x-kl-ajax-request': 'Ajax_Request',
      'x-requested-with': 'fetch',
      'x-xsrftoken': token,
      'x-zse-93': '101_3_3.0',
      'x-zse-96': xone,
      cookie: cookie,
      Referer: `https://zhida.zhihu.com/search/${session_id}`,
    };

    const sendBody = {
      knowledge_ids: [
        'KBT_GLOBAL',
        'KBT_ZHIHU',
        'KBT_PAPER',
        'KBT_PERSONAL_KNOWLEDGE_BASE',
      ],
      quiz_type: 'QT_CHAT',
      attachments: [],
      message_source_type: 'text',
      session_id: session_id,
      zhida_source: 'zhida',
      chat_model: 'CM_DEEP_SEEK_R1',
      message_content: message,
    };

    // 发送问题
    try {
      const sendRes = await axios.post(
        'https://zhida.zhihu.com/ai_ingress/ai_chat/send_message_v2',
        sendBody,
        { headers: sendHeaders },
      );

      const recvMessageId = sendRes.data?.recv_message?.message_id;
      await app.redis.set('recvMessageId', recvMessageId);
      if (!recvMessageId) {
        ctx.service.feishu['fs3']('发送失败，未获取 recv_message.message_id');
      }

      // 轮询等待响应
      const pollHeaders = {
        ...sendHeaders,
        'x-zse-96': xtwo,
      };
      // 用来存放后端推过来的完整 thinking 文本
      let thinkingSoFar = '';
      // 记录上次已经打印到控制台的 thinking 长度
      let lastThinkingLength = 0;
      // 用来存放后端推过来的完整 answer 文本
      let answerSoFar = '';
      // 记录上次已经发送的 answer 长度
      let lastAnswerLength = 0;
      const answerList = [];
      let done = false;
      const thinkingList = [];
      let thinking = '';
      let prefixPrinted = false;
      // 这个 Promise 链用来串行地打印各个 thinking 片段，
      // 但主循环不会 await 它，所以不会阻塞对 Answer 的判断
      let lastPrintPromise = Promise.resolve();
      // 不 await，这样主循环能马上继续往下走去检查 Answer/End

      // 打字机效果的辅助函数，返回一个 Promise，
      // 会等到这个片段全部打印完才 resolve
      function typeWriterPromise(text) {
        return new Promise(async (resolve) => {
          if (!prefixPrinted) {
            process.stdout.write('[Thinking] ');
            prefixPrinted = true;
          }
          for (const char of text) {
            process.stdout.write(char);
            await new Promise((r) => setTimeout(r, 10));
          }
          resolve();
        });
      }

      let stop_signal = stop === true ? `&stop_signal=true` : ``;
      await app.redis.set(
        'thinking_answer',
        JSON.stringify({ content: '', time: Date.now() }),
      );
      await app.redis.set(
        'thinking_answer',
        JSON.stringify({ content: '', time: Date.now() }),
      );
      while (!done) {
        await new Promise((resolve) => setTimeout(resolve, 1000)); // 每秒轮询一次
        const pollRes = await axios.get(
          `https://zhida.zhihu.com/ai_ingress/ai_chat/polling_message_v2?message_id=${recvMessageId}${stop_signal}`,
          { headers: pollHeaders },
        );
        if (stop === true) {
          break;
        }
        const events = pollRes.data;
        for (const event of events) {
          if (event.event === 'Answer' && event.data?.summary) {
            // 🔧 优化：实现Answer增量发送，就像thinking一样
            const currentAnswer = event.data.summary;
            // 计算这次刚出现的新增部分
            const answerDiff = currentAnswer.slice(lastAnswerLength);
            // 更新已保存的完整 answer，以及已发送长度
            answerSoFar = currentAnswer;
            lastAnswerLength = currentAnswer.length;

            // 保存完整答案到answerList（用于最终返回）
            answerList.push(currentAnswer);

            console.log('📝 Answer增量处理:');
            console.log('  - 完整长度:', currentAnswer.length);
            console.log('  - 新增长度:', answerDiff.length);
            console.log(
              '  - 新增内容预览:',
              answerDiff.substring(0, 50) + '...',
            );

            // 保存完整答案到Redis
            await app.redis.set(
              'thinking_answer',
              JSON.stringify({ content: currentAnswer, time: Date.now() }),
            );

            // 🚀 关键改动：只发送新增部分到socket
            if (app.io && answerDiff) {
              app.io
                .of('/thinkprocess')
                .emit('answer', { content: answerDiff, isIncremental: true });
              console.log(
                '💭 发送答案增量到socket:',
                answerDiff.substring(0, 50) + '...',
              );
            }
          } else if (event.event === 'Think' && event.data?.thinking) {
            const currentThinking = event.data.thinking;
            // 计算这次刚出现的新增部分
            const diff = currentThinking.slice(lastThinkingLength);
            // 更新已保存的完整 thinking，以及已打印长度
            thinkingSoFar = currentThinking;
            lastThinkingLength = currentThinking.length;

            // 新增：推送思考过程到redis和socket（只发送新增部分）
            if (diff) {
              await app.redis.set(
                'thinking_process',
                JSON.stringify({ content: currentThinking, time: Date.now() }),
              );
              if (app.io) {
                app.io.of('/thinkprocess').emit('thinking', { content: diff });
                // console.log('🧠 发送思考过程增量到socket:', diff.substring(0, 50) + '...');
              }
            }

            // 如果 diff 不为空，再排队给打字机效果
            if (diff) {
              lastPrintPromise = lastPrintPromise.then(() =>
                typeWriterPromise(diff),
              );
            }
          } else if (event.event === 'End') {
            done = true;
            if (app.io) {
              app.io
                .of('/thinkprocess')
                .emit('end_of_stream', { message: 'Stream ended' });
              console.log('🏁 发送结束标志到socket');
            }
            break;
          }
        }
      }
      // 打印最终写入的数据，便于调试转义问题
      console.log('最终写入data内容：', answerList[answerList.length - 1]);
      return {
        data: answerList[answerList.length - 1],
        status: stop ? 406 : 200,
      };
    } catch (e) {
      const text =
        e?.response?.data?.error?.message || e?.message || '未知错误';
      ctx.service.feishu['fs'](`知乎问答${model}` + text);
      if (text.includes('操作频率过快')) {
        await app.redis.set(key, '3', 'EX', 120); // EX 表示设置秒级过期时间
      }
      return {
        message: '重新更新',
        status: 400,
      };
    }
  }

  async hunyuan(prompt) {
    const url =
      'https://yuanbao.tencent.com/api/chat/beacede5-3217-4b65-87c3-a86eb5de6c21';
    const headers = {
      accept: '*/*',
      'content-type': 'text/plain;charset=UTF-8',
      'x-agentid': 'naQivTmsDa/beacede5-3217-4b65-87c3-a86eb5de6c21',
      'x-timestamp': Date.now().toString(),

      'accept-language': 'zh-CN,zh;q=0.9',
      'cache-control': 'no-cache',
      chat_version: 'v1',
      pragma: 'no-cache',
      priority: 'u=1, i',
      'sec-ch-ua':
        '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'x-bus-params-md5': '7aab21f25f2166df790fb1436eb5c446',
      'x-commit-tag': '8f613b8a',
      'x-hy92': '3270ace0997adf3504c549d002000000e19307',
      'x-hy93': '19215131623100f6997adf3504c549d0c7134108cf',
      'x-instance-id': '5',
      'x-kl-ajax-request': 'Ajax_Request',
      'x-language': 'zh-CN',
      'x-os_version': 'Windows(10)-Blink',
      'x-platform': 'win',
      'x-requested-with': 'XMLHttpRequest',
      'x-source': 'web',
      'x-uskey':
        'DCAxFeb05pDmvvCtOP3B8IOqDrUH5%2F8iba2rGlUpGHRtKIb47nMQesTDg30q2WNwYweR85Vp52Fp%0AvrekG7RB%2FIOvCfdPz9CqbubyO1SXD%2FX0LQdlhMLoGp6%2F4xQ3sXJ1eViA%2BY9p4IDwudOpN8OG8Vmi%0ACNKK4uHgM9%2FyNTKQDaDwKCN02vNqdxo%2F4TY3sXJ1eS3U7YTj52X5%2B9tnYHHXnwtzBrIK5vnsa9qj%0APBTFWNGqbX%2BgwbA7OBMwkpFq002mcl7G8GJ061DmrvHsNOPW7JEzCrSO5r8iZ62rGlTHX%2FZ1LQrt%0A2Mfje16%2F4pessQUhewRQ75vs4VDrrdW2O7OA72j5XaUE5rFvfL9jGl%2FTWGWGSWdBq%2Fute1%2Bz9wY3%0A23bkdT7f5kxm7Vg746FscYGb6U4mRoJTzLHzeNvsapaWB9GrfjwzmuU9Hwoiv1XWpDdSLI42kZK0%0AzhO86uFgOPtS8JCoJrTf5MZrZuh1PwhQUfCmfsam1MAseI%2Bz%2BSY0smN7Yw5D8512yAcvzvsxZGWJ%0A8VQzD6MH5vkpbu%2FqPBVDWfZtdCL4lvbmYJKz4BCqw0N2dA7A8Yxj5qRyrMQ0PZcV%2F2ioWoTb4dZw%0AavxiOBwXWaX1LCB0lMbuHThz%2BSY0tEN7Yx7f5kxi7Vg79HksPa4T7It%2BbNIP4Ht1bbx5Z3uBWNQd%0ALCuz2kqmcI%2FQ5lOs002meAkR%2Bkx67VPpud70KcPQ%2F1OqC%2Bwf4MPiZK22KAfBWMR9LR%2Fhl%2BTtdJxA%0Avq4yqQ1qbhic7G2%2BgAys69nxczErmpEyC%2B6P9fOXP%2BJlOB9RIfD3PDMDxaa%2FGsqoz08ygW9xehkc%0A75r1%2BsmU14gHKcEPuwn5E5fYzbizc6%2BUbJddBKPvLCB1m%2Bb8eqA%2F8XcmjsA0KBHI75u55JR7k%2Fe1%0APzPb4AX%2FXobf4MHgZ9bpOpBBUNR2KFV3h%2BJpcU615pGqwFf0eAsB7G%2F67VDoud7pYZOEkAOrCoME%0A9%2BD0M92yPlNED%2FP2Kib2ld%2FmGBw15xY0sFJ1YxER7kl06UCvufmpP7cB%2FIP4Uuadz6OwfKN2PlLT%0AU%2FczNsJ5q%2Bxhbqw89AY%2FtE2meh0R%2Bk267VFrud70dC%3D%3D',
      'x-webdriver': '0',
      'x-ybuitest': '0',
      cookie:
        'hy_source=web; hy_user=Wai4YrcVQzkcUrYr; hy_token=pQsh7gS6dZztGcOiiihR3UiijJqsBMsduQlFUNEybVqKetCvX3ZZM/jTe1e6PRGN; _qimei_uuid42=19215131623100f6997adf3504c549d0c7134108cf; _qimei_fingerprint=369e56a8d9b0a21305e3aba3ebe256be; _qimei_i_3=23c22dd6c70f0689c396ad39528775b5f5e7a6f7170d0684b38a2f582f94763e686535943989e28b92a0; _qimei_h38=3270ace0997adf3504c549d002000000e19307; _qimei_i_1=72e54e81975d508a92c2fe39528322b4a1eda1f116080686bddb28582493206c616337c03980e1dcd1b6d8f9; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2235551049%22%2C%22first_id%22%3A%2218be0c8827c1dcf-0415c5b4d9b9108-26031051-2073600-18be0c8827d10ac%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%A4%BE%E4%BA%A4%E7%BD%91%E7%AB%99%E6%B5%81%E9%87%8F%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMThiZTBjODgyN2MxZGNmLTA0MTVjNWI0ZDliOTEwOC0yNjAzMTA1MS0yMDczNjAwLTE4YmUwYzg4MjdkMTBhYyIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjM1NTUxMDQ5In0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%2235551049%22%7D%2C%22%24device_id%22%3A%2218be0c8827c1dcf-0415c5b4d9b9108-26031051-2073600-18be0c8827d10ac%22%7D',
      Referer:
        'https://yuanbao.tencent.com/chat/naQivTmsDa/beacede5-3217-4b65-87c3-a86eb5de6c21',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    };

    const data = {
      model: 'gpt_175B_0404',
      prompt,
      plugin: 'Adaptive',
      displayPrompt: prompt,
      displayPromptType: 1,
      options: {
        imageIntention: {
          needIntentionModel: true,
          backendUpdateFlag: 2,
          intentionStatus: true,
        },
      },
      agentId: 'naQivTmsDa',
      version: 'v2',
      chatModelId: 'deep_seek',
      supportFunctions: ['autoInternetSearch'],
    };

    let reasoningContent = '';
    let finalContent = '';

    try {
      const res = await axios.post(url, data, {
        headers,
        responseType: 'stream',
      });

      let reasoningContent = '';
      let finalContent = '';

      // 异步打印函数，fire-and-forget
      async function printTypingEffect(text) {
        for (const char of text) {
          process.stdout.write(char); // 终端逐字打印
          await new Promise((resolve) => setTimeout(resolve, 50)); // 每秒轮询一次
        }
      }

      const parser = createParser({
        onEvent(event) {
          const jsonStr = event.data;
          if (!jsonStr || !jsonStr.trim().startsWith('{')) return;

          try {
            const json = JSON.parse(jsonStr);
            if (json.type === 'think') {
              const newThink = json.content || '';
              reasoningContent += newThink;

              // 异步打印，不阻塞
              printTypingEffect(newThink).catch(() => {
                /* 打印异常忽略 */
              });
            } else if (json.type === 'text') {
              finalContent += json.msg || '';
            }
          } catch (e) {
            // 忽略解析错误
          }
        },
      });

      for await (const chunk of res.data) {
        parser.feed(chunk.toString());
      }

      return {
        status: 200,
        data: finalContent.trim(),
        reasoning_content: reasoningContent.trim(),
      };
    } catch (error) {
      this.logger.error('[Hunyuan API Error]', error);
      return {
        status: 500,
        message: '请求失败',
        error: error.message,
      };
    }
  }
}

module.exports = AiService;
