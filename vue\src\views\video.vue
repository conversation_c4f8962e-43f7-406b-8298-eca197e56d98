<script setup>
  import { onMounted } from 'vue';

  const switchbackground = (number) => {
    if (number === 1) {
      document.body.style.background = '#000000';
    } else if (number === 2) {
      document.body.style.background = '#fcf2d7 url(/bg_paper_mid.jpg)';
    }
  };
  onMounted(async () => {
    const wp = document.querySelector('.wp');
    console.log(window.innerHeight);
    console.log(wp.clientHeight);
  });
</script>

<template>
  <div class="left">
    <a-button @click="switchbackground(2)">羊皮</a-button>
    <a-button @click="switchbackground(1)">黑色</a-button>
  </div>
  <div class="wp">
    <div class="video">
      <dp url=" http://127.0.0.1:9001/1.mp4" :uid="Date.now()"></dp>
    </div>
  </div>
</template>

<style>
  body {
    background: #000000;
  }
  .dplayer {
    border-radius: 10px;
  }
  .wp {
    margin: 0 auto;
    width: 1080px; /* 宽度占满父容器 */
    max-width: 100vw; /* 最大宽度不超过视口宽度 */
  }
  .left {
    width: 100px;
    height: 100px;
  }
</style>
