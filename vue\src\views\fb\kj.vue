<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div v-show="showTop" class="top">
          <a-button @click="openfblx()">打开粉笔页面</a-button>
          <a-button @click="openfbbg()">打开粉笔报告</a-button>
          <a-button @click="togglean()">显示答案</a-button>
          <a-button @click="pushallans()">上传答案</a-button>
          <a-button>{{ total }}</a-button>
          <a-button @click="toggleTop()">隐藏顶部内容</a-button>
          <a-button :type="zuoti === true ? 'primary' : 'dashed'" @click="zuotimode()"
            >做题模式{{ zuoti === true ? '开' : '关' }}</a-button
          >
          <a-button :type="zuoti === true ? 'primary' : 'dashed'" @click="submit()">提交</a-button>
          <a-button @click="toggleBackground">背景</a-button>

          <el-select
            v-model="mode"
            placeholder="Select"
            style="width: 100px"
            @change="console.log(mode)"
          >
            <el-option v-for="item in options" :key="item" :label="item" :value="item" />
          </el-select>
          <!--          <a-input-->
          <!--            v-model:value="kaojuanid"-->
          <!--            placeholder="考卷id"-->
          <!--            @change="getData()"-->
          <!--          ></a-input>-->
          <a-input v-model:value="kjid1" placeholder="上传答案" @change="getData"></a-input>
          <!--          <a-textarea-->
          <!--            v-model:value="ids"-->
          <!--            placeholder="ids"-->
          <!--            @change="getData"-->
          <!--          ></a-textarea>-->
        </div>
        <p style="color: #00ff05" @click="toggleTop">==================</p>

        <div v-for="(item, index) in data" :key="index">
          <div v-if="isLoading">Loading...</div>
          <div v-if="!isLoading">
            <div v-if="item.index === 1" style="color: #00ff05">================</div>
            <div v-if="item.showmaterial" v-html="materials[item.materialIndexes].content"></div>
            <div v-html="item.content"></div>
            <div class="item">
              <a-row>
                <a-col :span="24">
                  <p class="an_a" v-html="item.accessories[0].options[0]"></p>
                </a-col>
                <a-col :span="24"
                  ><p class="an_b" v-html="item.accessories[0].options[1]"></p>
                </a-col>
                <a-col :span="24"
                  ><p class="an_c" v-html="item.accessories[0].options[2]"></p>
                </a-col>
                <a-col :span="24"
                  ><p class="an_d" v-html="item.accessories[0].options[3]"></p>
                </a-col>
              </a-row>
            </div>
            <p style="color: #00ff05" @click="toggleTop">==================</p>
            <div v-show="showContent" class="answer">
              <div>
                <span style="color: blue">{{ item.choice }}</span>
                {{ item.source }}{{ item.createdTime }}
              </div>
              <br />
              <div>
                <div v-html="item.solution"></div>
              </div>
              <div>
                <dp1 v-if="showvideo" :url="item.id" :uid="item.id"></dp1>
              </div>
              <p>==================</p>
            </div>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import { message } from 'ant-design-vue';
  import dp from '../../components/dp.vue';
  import dayjs from 'dayjs';
  const data = ref([]);
  const isLoading = ref(true);
  const current1 = ref(1);
  const showContent = ref(false);
  const pagetotal = ref(20);
  const pageSize3 = ref(4);
  const showTop = ref(true);
  const total = ref(210);
  const isdangerarr = ref([false, false, false, false]);
  const route = useRoute();
  const fontStyle = ref(16);
  const kaojuanid = ref('');
  const ids = ref('');
  const fbtimuid = ref(1);
  const daanid = ref('');
  const zuoti = ref(false);
  const isBackgroundActive = ref(false);
  const mode = ref('xingce');
  const options = ref(['xingce', 'jiaoshi']);
  const showvideo = ref(false);
  const materials = ref([]);
  const kjid1 = ref(0);
  const toggleBackground = async () => {
    if (isBackgroundActive.value) {
      document.body.style.background = '#fcf2d7 url(/bg_paper_mid.jpg)';
    } else {
      document.body.style.background = '';
    }
    isBackgroundActive.value = !isBackgroundActive.value;
  };
  const zuotimode = () => {
    zuoti.value = !zuoti.value;
  };
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const openfblx = () => {
    const type = route.query.type;
    let mode, mode1;
    if (type === 'js') {
      mode = 'jszgz';
      mode1 = 'jszgjy';
    } else {
      mode = 'xingce';
      mode1 = 'xingce';
    }
    window.open(
      `https://www.fenbi.com/spa/tiku/exam/practice/${mode}/${mode1}/` +
        fbtimuid.value +
        `/2?exerciseTimeMode=2`,
    );
  };
  const openfbbg = () => {
    const type = route.query.type;
    let mode, mode1;
    if (type === 'js') {
      mode = 'jszgz';
      mode1 = 'jszgjy';
    } else {
      mode = 'xingce';
      mode1 = 'xingce';
    }
    window.open(
      `https://www.fenbi.com/spa/tiku/report/exam/solution/${mode}/${mode1}/` +
        fbtimuid.value +
        `/2`,
    );
  };
  const updateFontSize = () => {
    const wpElement = document.querySelector('.wp');
    wpElement.style.fontSize = `${fontStyle.value}px`;
  };
  const handleKeyDown = async (event) => {
    if (event.key === 'ArrowLeft' || event.key === 'q') {
      if (current1.value > 1) {
        current1.value -= 1;
        await getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);
          await ansblack();
          showContent.value = false;
        }
      }
    } else if (event.key === 'ArrowRight' || event.key === 'e') {
      if (current1.value < 10000) {
        current1.value += 1;
        await getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);
          await ansblack();
          showContent.value = false;
        }
      }
    }
  };

  const right = () => {
    if (current1.value < 10000) {
      current1.value += 1;
      getData();
      ansblack();

      if (showContent.value) {
        isDanger(false);

        showContent.value = false;
      }
    }
  };

  const left = () => {
    if (current1.value > 1) {
      current1.value -= 1;
      getData();
      ansblack();

      if (showContent.value) {
        isDanger(false);

        showContent.value = false;
      }
    }
  };
  const toggleContent = async (event) => {
    if (event.key === 'w' || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      if (showContent.value) {
        // isDanger(false);
        showContent.value = false;
        await ansblack();
      } else {
        // ansred();
        // const showAnsButton = document.querySelector('.showans');
        // showAnsButton.click();
        showContent.value = true;
        await ansred();
      }
    }
  };

  const ansred = async () => {
    await ansblack();
    console.log(document.getElementsByClassName('an_a'));
    for (let i = 0; i < data.value.length; i++) {
      // console.log(data.value);
      if (data.value && data.value[i].choice === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'blue';
      }
      if (data.value && data.value[i].choice === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'blue';
      }
      if (data.value && data.value[i].choice === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'blue';
      }
      if (data.value && data.value[i].choice === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'blue';
      }
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'red';
      }
    }
  };

  const ansblack = async () => {
    for (let i = 0; i < data.value.length; i++) {
      document.getElementsByClassName('an_a')[i].style.color = 'black';
      document.getElementsByClassName('an_b')[i].style.color = 'black';
      document.getElementsByClassName('an_c')[i].style.color = 'black';
      document.getElementsByClassName('an_d')[i].style.color = 'black';
    }
  };

  const anscolor = async (index, answer) => {
    console.log(index, answer);

    // Clear previous colors for the current question
    const answers = ['A', 'B', 'C', 'D'];
    answers.forEach((ans) => {
      const className = `an_${ans.toLowerCase()}`;
      const element = document.getElementsByClassName(className)[index];
      if (element) {
        element.style.color = ''; // Reset color
      }
    });

    // Set the color of the selected answer to green
    const className = `an_${answer.toLowerCase()}`;
    const element = document.getElementsByClassName(className)[index];
    if (element) {
      element.style.color = '#2bc8a0';
    }
  };

  const togglean = async (answer) => {
    if (showContent.value) {
      await ansblack();
      isDanger(false);
      showContent.value = false;
    } else {
      await ansblack();
      await ansred();
      isDanger(answer);
      showContent.value = true;
    }
  };

  const pushans = async (id, answer, index) => {
    const url = '/egg/fbchoice';
    const type = route.query.type || 'sy';
    // console.log(type);
    const response = await axios.post(url, {
      id: id,
      choice: answer,
      type: type,
    });
    if (response.data.affectedRows !== 0) {
      message.success({
        content: `选择${answer}成功`,
        style: {
          marginTop: '80vh',
        },
      });
      await getData();
      await anscolor(index, answer);

      // await ansred();
      // await ansblack();
      // showContent.value = true;
    }
    if (zuoti.value === true) {
      const urlx = '/egg/fbincr';
      if (type === 'js') {
        mode.value = 'jszgjy';
      } else {
        mode.value = 'xingce';
      }
      const response = await axios.post(urlx, {
        id: id,
        choice: answer,
        mode: mode.value,
        index: index,
        kjid: route.query.kjid,
      });
      if (response.data.msg) {
        message.success({
          content: `做题模式选择${answer}成功`,
          style: {
            marginTop: '60vh',
          },
        });
        await getData();
        // await ansred();
        // await ansblack();
        // showContent.value = true;
      }
    }
  };
  const submit = async () => {
    const urlx = '/egg/fbsubmit';
    const type = route.query.type;
    if (type === 'js') {
      mode.value = 'jszgjy';
    } else {
      mode.value = 'xingce';
    }
    const response = await axios.post(urlx, {
      kjid: route.query.kjid,
      mode: mode.value,
    });
    if (response.data.msg) {
      message.success({
        content: `提交成功`,
        style: {
          marginTop: '80vh',
        },
      });
      // await getData();
      // await ansred();
      // await ansblack();
      // showContent.value = true;
    }
  };
  const pushallans = async () => {
    let list = data.value;
    let daan = daanid.value;
    let daanlist = daan.split('');
    daanlist = daanlist.map((item) => {
      if (+item === 1) {
        return 'A';
      }
      if (+item === 2) {
        return 'B';
      }
      if (+item === 3) {
        return 'C';
      }
      if (+item === 4) {
        return 'D';
      }
      return item; // This is necessary to return the unchanged item for other values
    });
    console.log(daanlist);
    for (let i = 0; i < list.length; i++) {}
    list.map((item, index) => {
      console.log(item.id, daanlist[index]);
      pushans(item.id, daanlist[index]);
    });
  };

  const isDanger = (answer) => {
    if (data.value && data.value.referenceAnswer === answer && answer !== false) {
      switch (answer) {
        case 'A':
          isdangerarr.value[0] = true;
          break;
        case 'B':
          isdangerarr.value[1] = true;
          break;
        case 'C':
          isdangerarr.value[2] = true;
          break;
        case 'D':
          isdangerarr.value[3] = true;
          break;
      }
      return true;
    } else if (data.value && answer === false) {
      isdangerarr.value.fill(false);
    }
    return false;
  };

  const getData = async () => {
    const per = route.query.per || pageSize3.value;
    const a = route.query.a || false;
    const id = route.query.id || 48905;
    const page = route.query.page || current1.value;
    const type = route.query.type || 'xingce';
    const gen = route.query.gen || 1;
    const kjid = route.query.kjid || kjid1.value;
    const z = route.query.type === 'zlfx' ? 1 : route.query.z || 0;
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/fbgetkj';
    let params = {
      per: per,
      page: page,
      id: id,
      z: z,
      type: type,
      isfive: 1,
      biao: kaojuanid.value,
      gen: gen,
      kjid: kjid,
      ids: ids.value,
    };
    console.log(params);
    // await ansblack();
    try {
      const response = await axios.get(url, { params });
      // if (response.data.length === 0) {
      //   await getData();
      //   return;
      // }
      if (a) {
        showContent.value = true;
      }
      materials.value = response.data.materials;

      let x = 1;
      let o = 0;
      for (let item of response.data.solutions) {
        item.content = item.content.replace('<p>', `<p>${x}.`);
        if (
          (item.materialIndexes &&
            response.data.solutions[o - 1]?.materialIndexes &&
            item.materialIndexes[0] !== response.data.solutions[o - 1].materialIndexes[0]) ||
          (item.materialIndexes && !response.data.solutions[o - 1].materialIndexes)
        ) {
          item.showmaterial = 1;
          console.log(
            o,
            item.materialIndexes,
            response.data.solutions[o - 1]?.materialIndexes,
            item.materialIndexes[0] !== response.data.solutions[o - 1].materialIndexes,
          );
        }

        item.accessories[0].options[0] =
          'A.' + item.accessories[0].options[0].replace(`fenbike.cn`, `fbstatic.cn`);
        item.accessories[0].options[1] =
          'B.' + item.accessories[0].options[1].replace(`fenbike.cn`, `fbstatic.cn`);
        item.accessories[0].options[2] =
          'C.' + item.accessories[0].options[2].replace(`fenbike.cn`, `fbstatic.cn`);
        item.accessories[0].options[3] =
          'D.' + item.accessories[0].options[3].replace(`fenbike.cn`, `fbstatic.cn`);
        item.createdTime = dayjs(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
        x++;
        o++;
      }
      data.value = response.data.solutions;
      showContent.value = false;
      isLoading.value = false;
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', toggleContent);
    getData();
    window.addEventListener('keydown', handleKeyDown);
    updateFontSize();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
  });
</script>

<style scoped>
  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }
  div {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
      'Noto Color Emoji';
    font-size: 20px;
  }
  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }
</style>
