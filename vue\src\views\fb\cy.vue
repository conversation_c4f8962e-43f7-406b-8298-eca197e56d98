<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div>
          <div v-for="(item, index) in data" :key="index">
            <div v-if="isLoading">Loading...</div>

            <div v-if="!isLoading" class="contentx">
              <div style="display: flex; gap: 10px">
                <div style="color: crimson">{{ item.sort }}-{{ item.count }}.{{ item.name }}</div>
                <div></div>
              </div>
              <div style="padding-top: 10px" v-text="item.jieshi1 + item.jieshi"></div>
              <p>====================================</p>
            </div>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script>
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';

  export default {
    name: 'Min',
    components: {
      fbtag,
      fbhiddentag,
    },
    setup() {
      const zltimu = ref([]);
      const data = ref([]);
      const isLoading = ref(true);
      const showTop = ref(true);
      const current1 = ref(1);
      const showContent = ref(false);
      const pagetotal = ref(20);
      const pageSize3 = ref(1);
      const total = ref(0);
      const isdangerarr = ref([false, false, false, false]);
      const route = useRoute();
      const fontStyle = ref(16);
      const value1 = ref(48644);
      const timu = ref('');
      const toggleTop = () => {
        showTop.value = !showTop.value;
      };
      const increaseFontSize = () => {
        fontStyle.value += 2;
        updateFontSize();
      };
      const updateFontSize = () => {
        const wpElement = document.querySelector('.wp');
        wpElement.style.fontSize = `${fontStyle.value}px`;
      };
      const handleKeyDown = (event) => {
        if (event.key === 'ArrowLeft' || event.key === 'q') {
          if (current1.value > 1) {
            current1.value -= 1;
            getData();
            if (showContent.value) {
              ansblack();
              isDanger(false);

              showContent.value = false;
            }
          }
        } else if (event.key === 'ArrowRight' || event.key === 'e') {
          if (current1.value < 10000) {
            current1.value += 1;
            getData();
            if (showContent.value) {
              ansblack();
              isDanger(false);

              showContent.value = false;
            }
          }
        }
      };

      const right = () => {
        if (current1.value < 10000) {
          current1.value += 1;
          getData();
          if (showContent.value) {
            ansblack();
            isDanger(false);

            showContent.value = false;
          }
        }
      };

      const left = () => {
        if (current1.value > 1) {
          current1.value -= 1;
          getData();
          if (showContent.value) {
            ansblack();
            isDanger(false);

            showContent.value = false;
          }
        }
      };
      const toggleContent = (event) => {
        if (event.key === 'w' || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
          if (showContent.value) {
            ansblack();
            isDanger(false);
            showContent.value = false;
          } else {
            ansred();
            const showAnsButton = document.querySelector('.showans');
            showAnsButton.click();
            showContent.value = true;
          }
        }
      };

      const ansred = () => {
        for (let i = 0; i < data.value.length; i++) {
          if (data.value && data.value[i].answer === 'A') {
            document.getElementsByClassName('an_a')[i].style.color = 'red';
          }
          if (data.value && data.value[i].answer === 'B') {
            document.getElementsByClassName('an_b')[i].style.color = 'red';
          }
          if (data.value && data.value[i].answer === 'C') {
            document.getElementsByClassName('an_c')[i].style.color = 'red';
          }
          if (data.value && data.value[i].answer === 'D') {
            document.getElementsByClassName('an_d')[i].style.color = 'red';
          }
        }
      };

      const ansblack = () => {
        for (let i = 0; i < data.value.length; i++) {
          if (data.value && data.value[i].answer === 'A') {
            document.getElementsByClassName('an_a')[i].style.color = 'black';
          }
          if (data.value && data.value[i].answer === 'B') {
            document.getElementsByClassName('an_b')[i].style.color = 'black';
          }
          if (data.value && data.value[i].answer === 'C') {
            document.getElementsByClassName('an_c')[i].style.color = 'black';
          }
          if (data.value && data.value[i].answer === 'D') {
            document.getElementsByClassName('an_d')[i].style.color = 'black';
          }
        }
      };

      const togglean = (answer) => {
        if (showContent.value) {
          ansblack();
          isDanger(false);
          showContent.value = false;
        } else {
          ansred();
          isDanger(answer);
          showContent.value = true;
        }
      };

      const pushans = async (answer) => {
        // const url = '/egg/minchoice';
        // data.value.choice = answer;
        // const response = await axios.post(url, data.value);
        // console.log(response);
      };
      const copyText = () => {
        let datax = data.value[0];
        console.log(datax);

        // 将<p>和</p>替换为换行符\n，并删除所有其他的HTML标签
        let contentWithoutHtml = datax.content
          .replace(/<p>/gi, '') // 删除开头的<p>标签
          .replace(/<\/p>/gi, '\n') // 将结束的</p>标签替换为换行符
          .replace(/<[^>]*>/g, ''); // 删除所有其他HTML标签

        // 构建最终的文本
        let text = `${contentWithoutHtml}${datax.answerone}\n${datax.answertwo}\n${datax.answerthree}\n${datax.answerfour}\n====================================
      \n`;

        // 将文本复制到剪贴板
        navigator.clipboard.writeText(text);
      };

      const isDanger = (answer) => {
        if (data.value && data.value.answer === answer && answer !== false) {
          switch (answer) {
            case 'A':
              isdangerarr.value[0] = true;
              break;
            case 'B':
              isdangerarr.value[1] = true;
              break;
            case 'C':
              isdangerarr.value[2] = true;
              break;
            case 'D':
              isdangerarr.value[3] = true;
              break;
          }
          return true;
        } else if (data.value && answer === false) {
          isdangerarr.value.fill(false);
        }
        return false;
      };
      const focus = () => {
        console.log('focus');
      };
      const handleChange = (value) => {
        console.log(`selected ${value}`);
      };
      const getData = async () => {
        const a = route.query.a || false;
        const z = route.query.z || 0;
        const limit = route.query.limit || 4;
        const biao = route.query.biao || `fbchengyuall`;
        const url = '/egg/fbcy';
        // const clipboardData = await navigator.clipboard.readText();
        // console.log('剪贴板', clipboardData);
        // timu.value = clipboardData;
        let params = { biao };
        console.log(params);
        try {
          const response = await axios.get(url, { params });
          let x = [];
          let i = 1;
          for (let item of response.data) {
            if (item.jieshi1 !== '' && item.jieshi !== '') {
              if (limit && item.name.length === +limit) {
                item.sort = i;
                x.push(item);
                i++;
              }
            }
          }
          data.value = x;
          console.log(data.value);
          showContent.value = false;
          isLoading.value = false;
        } catch (error) {
          console.error(error);
          isLoading.value = false;
        }
      };

      onMounted(() => {
        document.addEventListener('keydown', toggleContent);
        getData();
        window.addEventListener('keydown', handleKeyDown);
        updateFontSize();
      });

      onBeforeUnmount(() => {
        window.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keydown', toggleContent);
      });

      return {
        data,
        zltimu,
        isLoading,
        current1,
        showContent,
        pagetotal,
        pageSize3,
        total,
        left,
        right,
        togglean,
        getData,
        isDanger,
        isdangerarr,
        pushans,
        fontStyle,
        increaseFontSize,
        toggleTop,
        showTop,
        value1,
        focus,
        handleChange,
        timu,
        copyText,
      };
    },
  };
</script>

<style>
  body {
    background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
    font-family: 'SimSun', sans-serif;
  }

  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: black;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }

  .taginput {
    user-select: none;
    cursor: pointer;
  }
</style>
