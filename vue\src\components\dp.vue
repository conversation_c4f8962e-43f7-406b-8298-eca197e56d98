<template>
  <span><div :id="'video_' + uid"></div></span>
</template>

<script>
  import { defineComponent, onMounted, watch, nextTick } from 'vue';
  import DPlayer from 'dplayer';

  export default defineComponent({
    name: 'DpComponent',
    props: {
      url: String,
      uid: Number,
    },
    setup(props) {
      const initializePlayer = async () => {
        await nextTick();
        try {
          new DPlayer({
            container: document.getElementById('video_' + props.uid),
            preload: 'none',
            video: {
              url: props.url,
            },
          });
        } catch (error) {
          console.error(error);
        }
      };

      onMounted(() => {
        initializePlayer();
      });

      watch(
        () => props.url,
        (newUrl, oldUrl) => {
          if (newUrl !== oldUrl) {
            initializePlayer();
          }
        },
      );

      watch(
        () => props.uid,
        (newUid, oldUid) => {
          if (newUid !== oldUid) {
            initializePlayer();
          }
        },
      );

      return {
        initializePlayer,
      };
    },
  });
</script>

<style scoped></style>
